import 'package:flutter/material.dart';
import 'package:hotel_booking/constants/app_colors.dart';
import 'package:hotel_booking/constants/app_text_styles.dart';
import 'package:hotel_booking/models/hotel_offers.dart';
import 'package:hotel_booking/providers/booking_provider.dart';
import 'package:provider/provider.dart';

class DiscountCouponsPageWidget extends StatefulWidget {
  final double totalAmount;
  final Function(String, double) onCouponApplied;
  final BookingProvider bookingProvider;

  const DiscountCouponsPageWidget({
    Key? key,
    required this.totalAmount,
    required this.onCouponApplied,
    required this.bookingProvider,
  }) : super(key: key);

  @override
  State<DiscountCouponsPageWidget> createState() => _DiscountCouponsPageWidgetState();
}

class _DiscountCouponsPageWidgetState extends State<DiscountCouponsPageWidget> {
  final TextEditingController _couponController = TextEditingController();
  String? _selectedCouponCode;
  bool _isApplying = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();

    if (widget.bookingProvider.hotelOffers == null) {
      widget.bookingProvider.loadHotelOffers();
    }
  }

  @override
  void dispose() {
    _couponController.dispose();
    super.dispose();
  }

  double _calculateDiscount(Offer offer) {
    // For now, return the instant discount from the offer
    // You can implement more complex logic based on minimum order value, etc.
    if (offer.instantDiscount != null) {
      return offer.instantDiscount!.toDouble();
    }
    return 0.0;
  }

  void _applyCoupon(String code) {
    setState(() {
      _isApplying = true;
      _errorMessage = null;
    });

    // Simulate API call with a delay
    Future.delayed(const Duration(milliseconds: 800), () {
      final offers = widget.bookingProvider.hotelOffers?.data?.offers ?? [];
      final offer = offers.firstWhere(
        (o) => o.couponCode == code,
        orElse: () => Offer(), // Return empty offer if not found
      );

      if (offer.couponCode == null) {
        setState(() {
          _isApplying = false;
          _errorMessage = 'Invalid coupon code';
        });
        return;
      }

      final discount = _calculateDiscount(offer);
      if (discount <= 0) {
        setState(() {
          _isApplying = false;
          _errorMessage = 'This coupon is not applicable';
        });
        return;
      }

      setState(() {
        _isApplying = false;
        _selectedCouponCode = code;
      });

      widget.onCouponApplied(code, discount);
      Navigator.pop(context);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Apply Coupon'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(20),
          ),
        ),
      ),
      body: Consumer<BookingProvider>(
        builder: (context, bookingProvider, child) {
          final offers = bookingProvider.getUniqueOffers();

          return Column(
            children: [
              // Coupon input field
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _couponController,
                        decoration: InputDecoration(
                          hintText: 'Enter coupon code',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                        ),
                        textCapitalization: TextCapitalization.characters,
                      ),
                    ),
                    const SizedBox(width: 12),
                    ElevatedButton(
                      onPressed: _isApplying
                          ? null
                          : () {
                              if (_couponController.text.isNotEmpty) {
                                _applyCoupon(_couponController.text);
                              }
                            },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: _isApplying
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            )
                          : const Text('Apply'),
                    ),
                  ],
                ),
              ),

              // Error message
              if (_errorMessage != null)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Text(
                    _errorMessage!,
                    style: const TextStyle(color: Colors.red),
                  ),
                ),


              // Coupon list
              Expanded(
                child: bookingProvider.isLoading
                    ? const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CircularProgressIndicator(),
                            SizedBox(height: 16),
                            Text('Loading offers...'),
                          ],
                        ),
                      )
                    : bookingProvider.error != null
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.error_outline,
                                  size: 64,
                                  color: Colors.red,
                                ),
                                SizedBox(height: 16),
                                Text(
                                  'Error loading offers',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                SizedBox(height: 8),
                                Text(
                                  bookingProvider.error!,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(color: Colors.grey),
                                ),
                                SizedBox(height: 16),
                                ElevatedButton(
                                  onPressed: () => bookingProvider.loadHotelOffers(),
                                  child: Text('Retry'),
                                ),
                              ],
                            ),
                          )
                        : offers.isEmpty
                            ? const Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.local_offer_outlined,
                                      size: 64,
                                      color: Colors.grey,
                                    ),
                                    SizedBox(height: 16),
                                    Text(
                                      'No offers available',
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    SizedBox(height: 8),
                                    Text(
                                      'Check back later for new offers',
                                      style: TextStyle(color: Colors.grey),
                                    ),
                                  ],
                                ),
                              )
                            : ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: offers.length,
                        itemBuilder: (context, index) {
                          final offer = offers[index];
                          final discount = _calculateDiscount(offer);
                          final isApplicable = discount > 0;
                          final isSelected = _selectedCouponCode == offer.couponCode;

                          return Card(
                            elevation: 2,
                            margin: const EdgeInsets.only(bottom: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                              side: BorderSide(
                                color: isSelected ? AppColors.primary : Colors.transparent,
                                width: isSelected ? 2 : 0,
                              ),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                        decoration: BoxDecoration(
                                          color: AppColors.primary.withAlpha(30),
                                          borderRadius: BorderRadius.circular(4),
                                        ),
                                        child: Text(
                                          offer.couponCode ?? '',
                                          style: TextStyle(
                                            color: AppColors.primary,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                      const Spacer(),
                                      if (isApplicable)
                                        ElevatedButton(
                                          onPressed: () => _applyCoupon(offer.couponCode ?? ''),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: isSelected ? Colors.grey.shade300 : AppColors.primary,
                                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                            shape: RoundedRectangleBorder(
                                              borderRadius: BorderRadius.circular(6),
                                            ),
                                          ),
                                          child: Text(
                                            isSelected ? 'Applied' : 'Apply',
                                            style: TextStyle(
                                              color: isSelected ? Colors.black : Colors.white,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                  const SizedBox(height: 12),
                                  Text(
                                    offer.applyMessage ?? 'Special offer available',
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.info_outline,
                                        size: 16,
                                        color: Colors.grey.shade600,
                                      ),
                                      const SizedBox(width: 4),
                                      Expanded(
                                        child: Text(
                                          'Payment Type: ${offer.paymentType ?? 'N/A'}',
                                          style: TextStyle(
                                            color: Colors.grey.shade600,
                                            fontSize: 14,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  if (offer.termsAndConditions != null)
                                    Padding(
                                      padding: const EdgeInsets.only(top: 8.0),
                                      child: Text(
                                        offer.termsAndConditions!,
                                        style: TextStyle(
                                          color: Colors.grey.shade600,
                                          fontSize: 12,
                                        ),
                                        maxLines: 3,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  if (!isApplicable)
                                    Padding(
                                      padding: const EdgeInsets.only(top: 8.0),
                                      child: Text(
                                        'Not applicable for current booking',
                                        style: TextStyle(
                                          color: Colors.red.shade700,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ),
                                  if (isApplicable)
                                    Padding(
                                      padding: const EdgeInsets.only(top: 8.0),
                                      child: Text(
                                        'You save: ₹${discount.toStringAsFixed(0)}',
                                        style: TextStyle(
                                          color: Colors.green.shade700,
                                          fontSize: 14,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
              ),
            ],
          );
        },
      ),
    );
  }
}