import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import '../models/search_cities.dart';

class SearchCitiesProvider extends ChangeNotifier {
  SearchCitiesData? _searchCitiesData;
  List<SearchCity> _filteredCities = [];
  List<PopularPlace> _filteredPopularPlaces = [];
  String _searchQuery = '';
  bool _isLoading = false;
  String? _currentLocation;
  String? _error;

  // Getters
  SearchCitiesData? get searchCitiesData => _searchCitiesData;
  List<SearchCity> get filteredCities => _filteredCities;
  List<PopularPlace> get filteredPopularPlaces => _filteredPopularPlaces;
  String get searchQuery => _searchQuery;
  bool get isLoading => _isLoading;
  String? get currentLocation => _currentLocation;
  String? get error => _error;

  // Load search cities data from JSON
  Future<void> loadSearchCitiesData() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final String jsonString = await rootBundle.loadString('assets/json/serachcities.json');
      final Map<String, dynamic> jsonData = json.decode(jsonString);
      
      _searchCitiesData = SearchCitiesData.fromJson(jsonData);
      _filteredCities = _searchCitiesData!.cities;
      _filteredPopularPlaces = _searchCitiesData!.popularPlaces;
      
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to load cities data: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  // Search functionality
  void searchCities(String query) {
    _searchQuery = query;
    
    if (_searchCitiesData == null) return;

    if (query.isEmpty) {
      _filteredCities = _searchCitiesData!.cities;
      _filteredPopularPlaces = _searchCitiesData!.popularPlaces;
    } else {
      final lowercaseQuery = query.toLowerCase();
      
      _filteredCities = _searchCitiesData!.cities.where((city) {
        return city.name.toLowerCase().contains(lowercaseQuery) ||
               city.country.toLowerCase().contains(lowercaseQuery);
      }).toList();

      _filteredPopularPlaces = _searchCitiesData!.popularPlaces.where((place) {
        return place.name.toLowerCase().contains(lowercaseQuery) ||
               place.city.toLowerCase().contains(lowercaseQuery) ||
               place.country.toLowerCase().contains(lowercaseQuery);
      }).toList();
    }
    
    notifyListeners();
  }

  // Get current location
  Future<void> getCurrentLocation() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        _error = 'Location services are disabled.';
        _isLoading = false;
        notifyListeners();
        return;
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          _error = 'Location permissions are denied';
          _isLoading = false;
          notifyListeners();
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _error = 'Location permissions are permanently denied, we cannot request permissions.';
        _isLoading = false;
        notifyListeners();
        return;
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // Get address from coordinates
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        _currentLocation = place.locality ?? place.administrativeArea ?? 'Current Location';
      } else {
        _currentLocation = 'Current Location';
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to get current location: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  // Clear search
  void clearSearch() {
    _searchQuery = '';
    if (_searchCitiesData != null) {
      _filteredCities = _searchCitiesData!.cities;
      _filteredPopularPlaces = _searchCitiesData!.popularPlaces;
    }
    notifyListeners();
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Get popular cities (first 10 cities sorted by property count)
  List<SearchCity> get popularCities {
    if (_searchCitiesData == null) return [];
    
    List<SearchCity> cities = List.from(_searchCitiesData!.cities);
    cities.sort((a, b) => b.propertyCount.compareTo(a.propertyCount));
    return cities.take(10).toList();
  }
}
