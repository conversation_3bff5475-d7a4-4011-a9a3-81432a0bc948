import 'package:flutter/material.dart';
import 'package:hotel_booking/screens/authentication/forgot_password_screen.dart';
import 'package:hotel_booking/screens/authentication/login_screen.dart';
import 'package:hotel_booking/screens/authentication/otp_verification_screen.dart';
import 'package:hotel_booking/screens/authentication/reset_password_screen.dart';
import 'package:hotel_booking/screens/booking%20screen/booking_screen.dart';
import 'package:hotel_booking/screens/homescreen/homescreen.dart';
import 'package:hotel_booking/screens/hotel%20detail%20screen/hotel_detail_screen.dart';
import 'package:hotel_booking/screens/hotel%20list%20screen/hotel_list_screen.dart';
import 'package:hotel_booking/screens/itinerary%20screen/itinerary_screen.dart';
import 'package:hotel_booking/screens/payment%20interface/payment_screen.dart';
import 'package:hotel_booking/screens/profile%20screen/profile_screen.dart';
import 'package:hotel_booking/screens/splash%20screen/splash_screen.dart';

/// Class that manages all routes in the application
class AppRoutes {
  // Route names as constants
  static const String home = '/';
  static const String login = '/login';
  static const String signup = '/signup';
  static const String forgotPassword = '/forgot-password';
  static const String otpVerification = '/otp-verification';
  static const String resetPassword = '/reset-password';
  static const String hotelList = '/hotel-list';
  static const String hotelDetail = '/hotel-detail';
  static const String booking = '/booking';
  static const String profile = '/profile';
  static const String favorites = '/favorites';
  static const String itinerary = '/itinerary';
  static const String payment = '/payment';
  static const String splash = '/splash';

  /// Generate the application routes
  static Map<String, WidgetBuilder> getRoutes() {
    return {
      home: (context) => const HomeScreen(),
      login: (context) => const LoginScreen(),
      // signup: (context) => const SignupScreen(),
      forgotPassword: (context) => const ForgotPasswordScreen(),
      profile: (context) => const ProfileScreen(),
      hotelList: (context) => const HotelListScreen(),
      booking: (context) => const BookingScreen(),
    
      splash: (context) =>  SplashScreen(),
      // Routes that require parameters are handled in generateRoute
    };
  }

  /// Handle routes that require parameters
  static Route<dynamic>? generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case otpVerification:
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder: (context) => OTPVerificationScreen(
            email: args['email'],
            isFromForgotPassword: args['isFromForgotPassword'],
          ),
        );

      case resetPassword:
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder: (context) => ResetPasswordScreen(
            email: args['email'],
          ),
        );

      case payment:
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder: (context) => PaymentScreen(
            totalAmount: args['totalAmount'],
            hotelName: args['hotelName'],
            roomType: args['roomType'],
            checkInDate: args['checkInDate'],
            checkOutDate: args['checkOutDate'],
          ),
        );

      case itinerary:
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder: (context) => ItineraryScreen(
            bookingId: args['bookingId'],
            hotelName: args['hotelName'],
            roomType: args['roomType'],
            checkInDate: args['checkInDate'],
            checkOutDate: args['checkOutDate'],
            numberOfGuests: args['numberOfGuests'],
            totalAmount: args['totalAmount'],
            guestName: args['guestName'],
            guestEmail: args['guestEmail'],
          ),
        );

      case hotelDetail:
        return MaterialPageRoute(
          builder: (context) => const HotelDetailScreen(),
        );

      default:
        return null;
    }
  }

  /// Navigate to OTP verification screen
  static void navigateToOtpVerification(BuildContext context, String email, bool isFromForgotPassword) {
    Navigator.pushNamed(
      context,
      otpVerification,
      arguments: {
        'email': email,
        'isFromForgotPassword': isFromForgotPassword,
      },
    );
  }

  /// Navigate to reset password screen
  static void navigateToResetPassword(BuildContext context, String email) {
    Navigator.pushNamed(
      context,
      resetPassword,
      arguments: {
        'email': email,
      },
    );
  }

  /// Navigate to payment screen
  static void navigateToPayment(
    BuildContext context, {
    required double totalAmount,
    required String hotelName,
    required String roomType,
    required String checkInDate,
    required String checkOutDate,
  }) {
    Navigator.pushNamed(
      context,
      payment,
      arguments: {
        'totalAmount': totalAmount,
        'hotelName': hotelName,
        'roomType': roomType,
        'checkInDate': checkInDate,
        'checkOutDate': checkOutDate,
      },
    );
  }

  /// Navigate to itinerary screen
  static void navigateToItinerary(
    BuildContext context, {
    required String bookingId,
    required String hotelName,
    required String roomType,
    required String checkInDate,
    required String checkOutDate,
    required int numberOfGuests,
    required double totalAmount,
    required String guestName,
    required String guestEmail,
  }) {
    Navigator.pushNamed(
      context,
      itinerary,
      arguments: {
        'bookingId': bookingId,
        'hotelName': hotelName,
        'roomType': roomType,
        'checkInDate': checkInDate,
        'checkOutDate': checkOutDate,
        'numberOfGuests': numberOfGuests,
        'totalAmount': totalAmount,
        'guestName': guestName,
        'guestEmail': guestEmail,
      },
    );
  }

  /// Navigate to hotel detail screen
  static void navigateToHotelDetail(BuildContext context) {
    Navigator.pushNamed(context, hotelDetail);
  }
}
