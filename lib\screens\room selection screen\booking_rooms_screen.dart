import 'package:flutter/material.dart';
import 'package:hotel_booking/constants/app_colors.dart';
import 'package:hotel_booking/models/hotel_details.dart';
import 'package:hotel_booking/providers/room_selection_provider.dart';
import 'package:hotel_booking/screens/booking%20screen/booking_screen.dart';
import 'package:hotel_booking/screens/room%20selection%20screen/widget/roomcard_widget.dart';
import 'package:provider/provider.dart';

class BookingRoomsScreen extends StatefulWidget {
  final InventoryInfoList? hotel;

  const BookingRoomsScreen({
    super.key,
    this.hotel,
  });

  @override
  State<BookingRoomsScreen> createState() => _BookingRoomsScreenState();
}

class _BookingRoomsScreenState extends State<BookingRoomsScreen>
    with TickerProviderStateMixin {
  String selectedRoomType = 'Deluxe Room';
  bool _isExpanded = false;
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<RoomSelectionProvider>(context, listen: false)
          .loadHotelRoomsFromJson();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  String _selectedSortOption = 'free_breakfast';
  final List<Map<String, dynamic>> _sortOptions = [
    {
      'id': 'free_breakfast',
      'label': 'Free Breakfast',
      'icon': Icons.free_breakfast
    },
    {
      'id': 'free_parking_wifi_breakfast',
      'label': 'Free Parking, WiFi & Breakfast',
      'icon': Icons.local_parking
    },
    {'id': 'breakfast', 'label': 'Breakfast', 'icon': Icons.breakfast_dining},
    {
      'id': 'bed_and_breakfast',
      'label': 'Bed & Breakfast',
      'icon': Icons.hotel
    },
    {
      'id': 'free_parking_wifi_room',
      'label': 'Free Parking, WiFi & Room Only',
      'icon': Icons.wifi
    },
    {'id': 'room_only', 'label': 'Room Only', 'icon': Icons.bedroom_parent},
  ];

  Widget _buildSortOptionsBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      child: Row(
        children: [
          Container(
            height: 50,
            width: 50,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: AppColors.primary,
            ),
            child: const Icon(Icons.sort, color: Colors.white),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: _sortOptions.map((option) {
                  final bool isSelected = _selectedSortOption == option['id'];
                  return InkWell(
                    onTap: () {
                      setState(() {
                        _selectedSortOption = option['id'] as String;
                      });
                    },
                    child: Container(
                      height: 50,
                      margin: const EdgeInsets.only(right: 10),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: isSelected ? AppColors.primary : Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isSelected
                              ? AppColors.secondary
                              : Colors.grey.shade300,
                        ),
                        boxShadow: isSelected
                            ? [
                                BoxShadow(
                                  color:
                                      AppColors.secondary.withAlpha(51),
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ]
                            : null,
                      ),
                      child: Row(
                        children: [
                          Icon(
                            option['icon'] as IconData,
                            size: 16,
                            color: isSelected
                                ? Colors.white
                                : Colors.grey.shade700,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            option['label'] as String,
                            style: TextStyle(
                              fontSize: 13,
                              fontWeight: isSelected
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              color: isSelected
                                  ? Colors.white
                                  : Colors.grey.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<RoomSelectionProvider>(
      builder: (context, provider, child) {
        return Scaffold(
          backgroundColor: AppColors.divider,
          appBar: AppBar(
            title: const Text('Available Rooms'),
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
            elevation: 0,
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(
                bottom: Radius.circular(20),
              ),
            ),
          ),
          body: Column(
            children: [
              _buildSortOptionsBar(),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  InkWell(
                    onTap: () {
                      setState(() {
                        _selectedSortOption = 'all';
                      });
                    },
                    child: Row(
                      children: [
                        Icon(Icons.filter_list_off,
                            size: 18, color: AppColors.secondary),
                        const SizedBox(width: 4),
                        Text(
                          'Clear Filters',
                          style: TextStyle(
                            decoration: TextDecoration.underline,
                            fontSize: 16,
                            color: AppColors.secondary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 20),
                ],
              ),
              Expanded(
                child: ListView.separated(
                  itemCount: provider.rooms.length,
                  itemBuilder: (context, index) => RoomcardWidget(
                    room: provider.rooms[index],
                    roomSelectionProvider: provider,
                  ),
                  separatorBuilder: (context, index) => const SizedBox(
                    height: 20,
                  ),
                ),
              ),
              if (provider.selectedRoomOptions.isNotEmpty)
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.neutralDark.withAlpha(13),
                        blurRadius: 10,
                        offset: const Offset(0, -5),
                      ),
                    ],
                  ),
                  child: _buildBottomSection(provider),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBottomSection(RoomSelectionProvider provider) {
    final hasSelectedRooms = provider.selectedRoomOptions.isNotEmpty;
    final totalPrice = provider.totalPrice;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (_isExpanded)
          SizeTransition(
            sizeFactor: _animation,
            child: Column(
              children: provider.selectedRoomOptions
                  .map((selectedOption) => Container(
                        margin: const EdgeInsets.only(bottom: 8),
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey.shade200),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    selectedOption.room.name ?? 'Room',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 14,
                                    ),
                                  ),
                                  const SizedBox(height: 2),
                                  Text(
                                    selectedOption.roomOption.name ?? 'Option',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Text(
                                  'Qty: ${selectedOption.quantity}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontSize: 12,
                                  ),
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  '₹${((selectedOption.roomOption.fareDetail?.totalPrice ?? 0.0) * selectedOption.quantity).toInt()}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                    color: AppColors.primary,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ))
                  .toList(),
            ),
          ),
        Row(
          children: [
            InkWell(
              onTap: hasSelectedRooms ? _toggleExpansion : null,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.primary),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Text(
                      '₹${totalPrice.toInt()}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.neutralDark,
                      ),
                    ),
                    const SizedBox(width: 10),
                    AnimatedRotation(
                      turns: _isExpanded ? 0.5 : 0,
                      duration: const Duration(milliseconds: 300),
                      child: const Icon(
                        Icons.expand_more,
                        size: 18,
                        color: AppColors.neutralDark,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: ElevatedButton(
                onPressed: hasSelectedRooms
                    ? () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => BookingScreen(
                              hotel: widget.hotel,
                              roomType: provider.selectedRoomOptions.first.room
                                      .name ??
                                  'Selected Room',
                              checkInDate:
                                  DateTime.now().add(const Duration(days: 1)),
                              checkOutDate:
                                  DateTime.now().add(const Duration(days: 3)),
                              adultCount: 2,
                              childCount: 0,
                            ),
                          ),
                        );
                      }
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      hasSelectedRooms ? AppColors.primary : Colors.grey,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  hasSelectedRooms ? 'Reserve Rooms' : 'Select Rooms First',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
