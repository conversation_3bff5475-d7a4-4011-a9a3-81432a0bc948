import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hotel_booking/constants/app_colors.dart';
import 'package:hotel_booking/models/hotel_reviews.dart';

class ReviewsTab extends StatefulWidget {
  const ReviewsTab({Key? key}) : super(key: key);

  @override
  State<ReviewsTab> createState() => _ReviewsTabState();
}

class _ReviewsTabState extends State<ReviewsTab> {
  HotelReviews? hotelReviews;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadHotelReviews();
  }

  Future<void> _loadHotelReviews() async {
    try {
      final String jsonString = await rootBundle.loadString('assets/json/hotelreviews.json');
      final Map<String, dynamic> jsonData = json.decode(jsonString);
      setState(() {
        hotelReviews = HotelReviews.fromJson(jsonData);
        isLoading = false;
      });
    } catch (e) {
      print('Error loading hotel reviews: $e');
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (hotelReviews == null || hotelReviews!.data.reviewList.isEmpty) {
      return const Center(
        child: Text('No reviews available'),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('Reviews'),
        _buildRatingOverview(),
        const SizedBox(height: 16),
        _buildReviewsList(),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildRatingOverview() {
    final reviews = hotelReviews!.data.reviewList;
    final averageRating = _calculateAverageRating(reviews);
    final ratingText = _getRatingText(averageRating);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                averageRating.toStringAsFixed(1),
                style: const TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    ratingText,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'Based on ${reviews.length} reviews',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildCategoryBreakdown(reviews),
        ],
      ),
    );
  }

  double _calculateAverageRating(List<Review> reviews) {
    if (reviews.isEmpty) return 0.0;
    double total = reviews.fold(0.0, (sum, review) => sum + review.ratingAsDouble);
    return total / reviews.length;
  }

  String _getRatingText(double rating) {
    if (rating >= 9.0) return 'Excellent';
    if (rating >= 8.0) return 'Very Good';
    if (rating >= 7.0) return 'Good';
    if (rating >= 6.0) return 'Fair';
    return 'Poor';
  }

  Widget _buildCategoryBreakdown(List<Review> reviews) {
    final categoryRatings = <String, List<double>>{};

    for (final review in reviews) {
      final category = review.category;
      if (!categoryRatings.containsKey(category)) {
        categoryRatings[category] = [];
      }
      categoryRatings[category]!.add(review.ratingAsDouble);
    }

    return Column(
      children: categoryRatings.entries.map((entry) {
        final category = entry.key;
        final ratings = entry.value;
        final averageRating = ratings.fold(0.0, (sum, rating) => sum + rating) / ratings.length;

        return _buildRatingBar(category, averageRating);
      }).toList(),
    );
  }

  Widget _buildRatingBar(String label, double rating) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Stack(
              children: [
                Container(
                  height: 8,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                FractionallySizedBox(
                  widthFactor: rating / 10,
                  child: Container(
                    height: 8,
                    decoration: BoxDecoration(
                      color: _getRatingColor(rating),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          Text(
            rating.toString(),
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Color _getRatingColor(double rating) {
    if (rating >= 9) return Colors.green;
    if (rating >= 8) return Colors.green[300]!;
    if (rating >= 7) return Colors.amber;
    if (rating >= 6) return Colors.orange;
    return Colors.red;
  }

  Widget _buildReviewsList() {
    final reviews = hotelReviews!.data.reviewList;
    final displayReviews = reviews.take(5).toList(); // Show first 5 reviews

    return Column(
      children: [
        ...displayReviews.map((review) => _buildReviewItem(review)),
        const SizedBox(height: 16),
        if (reviews.length > 5)
          Center(
            child: TextButton(
              onPressed: () {
                _showAllReviews(reviews);
              },
              child: Text(
                'View all ${reviews.length} reviews',
                style: const TextStyle(
                  color: Colors.blue,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        const SizedBox(height: 100), // Bottom padding for scrolling
      ],
    );
  }

  void _showAllReviews(List<Review> reviews) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.9,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              Container(
                margin: const EdgeInsets.symmetric(vertical: 10),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  'All Reviews (${reviews.length})',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Expanded(
                child: ListView.builder(
                  controller: scrollController,
                  itemCount: reviews.length,
                  itemBuilder: (context, index) => _buildReviewItem(reviews[index]),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildReviewItem(Review review) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                backgroundColor: _getRatingColor(review.ratingAsDouble),
                radius: 20,
                child: Text(
                  review.reviewerInitials,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          review.reviewerName.isNotEmpty ? review.reviewerName : 'Anonymous',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          review.categoryIcon,
                          style: const TextStyle(fontSize: 16),
                        ),
                      ],
                    ),
                    Row(
                      children: [
                        Text(
                          review.category,
                          style: const TextStyle(
                            color: Colors.grey,
                            fontSize: 12,
                          ),
                        ),
                        if (review.location != null && review.location!.isNotEmpty) ...[
                          const Text(' • ', style: TextStyle(color: Colors.grey, fontSize: 12)),
                          Text(
                            review.location!,
                            style: const TextStyle(
                              color: Colors.grey,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getRatingColor(review.ratingAsDouble),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  review.ratingAsDouble.toStringAsFixed(1),
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          if (review.title.isNotEmpty) ...[
            const SizedBox(height: 12),
            Text(
              review.title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
          const SizedBox(height: 8),
          Text(
            review.reviewComment,
            style: const TextStyle(
              fontSize: 14,
              height: 1.5,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Reviewed on ${review.reviewDate}',
            style: const TextStyle(
              color: Colors.grey,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }
}