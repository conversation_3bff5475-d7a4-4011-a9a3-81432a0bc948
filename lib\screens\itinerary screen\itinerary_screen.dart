import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hotel_booking/constants/app_colors.dart';
import 'package:hotel_booking/constants/app_text_styles.dart';
import 'package:hotel_booking/providers/room_selection_provider.dart';
import 'package:hotel_booking/widgets/custombutton_widget.dart';

class ItineraryScreen extends StatelessWidget {
  final String bookingId;
  final String hotelName;
  final String roomType;
  final String checkInDate;
  final String checkOutDate;
  final int numberOfGuests;
  final double totalAmount;
  final String guestName;
  final String guestEmail;
  final List<SelectedRoomOption>? selectedRoomOptions;

  const ItineraryScreen({
    Key? key,
    required this.bookingId,
    required this.hotelName,
    required this.roomType,
    required this.checkInDate,
    required this.checkOutDate,
    required this.numberOfGuests,
    required this.totalAmount,
    required this.guestName,
    required this.guestEmail,
    this.selectedRoomOptions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text('Booking Itinerary'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(20),
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Sharing itinerary...')),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Downloading itinerary...')),
              );
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Booking confirmation header
            _buildConfirmationHeader(),

            // Booking details
            _buildBookingDetails(),

            // Hotel details
            _buildHotelDetails(),

            // Room details
            _buildRoomDetails(),

            // Amenities
            _buildAmenities(),

            // Guest details
            _buildGuestDetails(),

            // Payment details
            _buildPaymentDetails(),

            // Policies
            _buildPolicies(),

            // Action buttons
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildConfirmationHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.primary,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(30),
          bottomRight: Radius.circular(30),
        ),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.check_circle,
            color: Colors.white,
            size: 60,
          ),
          const SizedBox(height: 16),
          Text(
            'Booking Confirmed!',
            style: AppTextStyles.headline2.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Booking ID: $bookingId',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(30),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.calendar_today,
                  color: Colors.white,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  '$checkInDate - $checkOutDate',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBookingDetails() {
    return _buildSection(
      title: 'Booking Details',
      icon: Icons.confirmation_number,
      child: Column(
        children: [
          _buildDetailRow('Booking ID', bookingId),
          _buildDetailRow('Check-in', '$checkInDate (From 2:00 PM)'),
          _buildDetailRow('Check-out', '$checkOutDate (Until 12:00 PM)'),
          _buildDetailRow('Number of Nights', '3 Nights'),
          _buildDetailRow('Number of Guests', '$numberOfGuests Guests'),
          _buildDetailRow('Booking Status', 'Confirmed'),
        ],
      ),
    );
  }

  Widget _buildHotelDetails() {
    return _buildSection(
      title: 'Hotel Details',
      icon: Icons.hotel,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Hotel image
          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Image.asset(
              'assets/images/sara-dubler-Koei_7yYtIo-unsplash.jpg',
              height: 150,
              width: double.infinity,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                height: 150,
                width: double.infinity,
                color: Colors.grey.shade300,
                child: const Icon(
                  Icons.image,
                  size: 50,
                  color: Colors.grey,
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Hotel name and rating
          Row(
            children: [
              Expanded(
                child: Text(
                  hotelName,
                  style: AppTextStyles.headline3.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.accent,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.star,
                      color: Colors.white,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '4.8',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // Hotel address
          Row(
            children: [
              const Icon(
                Icons.location_on,
                size: 16,
                color: Colors.grey,
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  'Palm Jumeirah, Dubai, United Arab Emirates',
                  style: TextStyle(
                    color: Colors.grey.shade700,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Contact info
          _buildDetailRow('Phone', '+971 4 123 4567'),
          _buildDetailRow('Email', '<EMAIL>'),
          _buildDetailRow('Website', 'www.luxuryresortspa.com'),
        ],
      ),
    );
  }

  Widget _buildRoomDetails() {
    return _buildSection(
      title: 'Room Details',
      icon: Icons.bed,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Room type and occupancy
          _buildDetailRow('Room Type', roomType),
          _buildDetailRow('Bed Type', '1 King Bed'),
          _buildDetailRow('Room Size', '41 m²'),
          _buildDetailRow('Max Occupancy', '2 Adults, 2 Children'),

          const SizedBox(height: 16),
          const Divider(),
          const SizedBox(height: 16),

          // Room inclusions
          Text(
            'Room Inclusions',
            style: AppTextStyles.subtitle1.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),

          // Inclusions grid
          Wrap(
            spacing: 10,
            runSpacing: 10,
            children: [
              _buildInclusionItem('Breakfast Included'),
              _buildInclusionItem('Free Wi-Fi'),
              _buildInclusionItem('Air Conditioning'),
              _buildInclusionItem('Flat-screen TV'),
              _buildInclusionItem('Private Bathroom'),
              _buildInclusionItem('Mini Bar'),
              _buildInclusionItem('Safe'),
              _buildInclusionItem('Room Service'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAmenities() {
    return _buildSection(
      title: 'Resort Amenities',
      icon: Icons.pool,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Amenities grid
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            childAspectRatio: 4,
            crossAxisSpacing: 10,
            mainAxisSpacing: 10,
            children: [
              _buildAmenityItem(Icons.pool, 'Swimming Pool'),
              _buildAmenityItem(Icons.spa, 'Spa & Wellness'),
              _buildAmenityItem(Icons.fitness_center, 'Fitness Center'),
              _buildAmenityItem(Icons.restaurant, 'Restaurant'),
              _buildAmenityItem(Icons.local_bar, 'Bar/Lounge'),
              _buildAmenityItem(Icons.beach_access, 'Private Beach'),
              _buildAmenityItem(Icons.directions_car, 'Free Parking'),
              _buildAmenityItem(Icons.business_center, 'Business Center'),
              _buildAmenityItem(Icons.child_care, 'Kids Club'),
              _buildAmenityItem(Icons.room_service, '24h Room Service'),
            ],
          ),

          const SizedBox(height: 16),
          const Divider(),
          const SizedBox(height: 16),

          // Activities
          Text(
            'Available Activities',
            style: AppTextStyles.subtitle1.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),

          // Activities list
          Column(
            children: [
              _buildActivityItem('Water Sports', 'Jet skiing, parasailing, and more'),
              _buildActivityItem('Desert Safari', 'Experience the Arabian desert'),
              _buildActivityItem('Golf', 'World-class golf courses nearby'),
              _buildActivityItem('City Tours', 'Explore Dubai\'s attractions'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildGuestDetails() {
    return _buildSection(
      title: 'Guest Details',
      icon: Icons.person,
      child: Column(
        children: [
          _buildDetailRow('Guest Name', guestName),
          _buildDetailRow('Email', guestEmail),
          _buildDetailRow('Phone', '+91 98765 43210'),
        ],
      ),
    );
  }

  Widget _buildPaymentDetails() {
    return _buildSection(
      title: 'Payment Details',
      icon: Icons.payment,
      child: Column(
        children: [
          _buildDetailRow('Payment Method', 'Credit Card (XXXX-XXXX-XXXX-1234)'),
          _buildDetailRow('Payment Status', 'Paid'),
          _buildDetailRow('Transaction ID', 'TXN123456789'),
          const SizedBox(height: 16),
          const Divider(),
          const SizedBox(height: 16),

          // Price breakdown
          _buildPriceRow('Room Charges (3 nights)', '\$360.00'),
          const SizedBox(height: 8),
          _buildPriceRow('Taxes & Fees', '\$40.00'),
          const SizedBox(height: 8),
          _buildPriceRow('Discount Applied', '-\$0.00', isDiscount: true),
          const SizedBox(height: 8),
          _buildPriceRow('Total Amount Paid', '\$${totalAmount.toStringAsFixed(2)}', isTotal: true),
        ],
      ),
    );
  }

  Widget _buildPolicies() {
    return _buildSection(
      title: 'Policies',
      icon: Icons.info_outline,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPolicyItem(
            'Check-in & Check-out',
            'Check-in time starts at 2 PM. Check-out time is 12 PM. Early check-in and late check-out are subject to availability and may incur additional charges.',
          ),
          _buildPolicyItem(
            'Cancellation Policy',
            'Free cancellation until April 26, 2025 (48 hours before check-in). After that, cancellation will incur a fee equivalent to the first night\'s stay.',
          ),
          _buildPolicyItem(
            'Children & Extra Beds',
            'Children of all ages are welcome. Children under 12 years stay free when using existing beds.',
          ),
          _buildPolicyItem(
            'Pets',
            'Pets are not allowed.',
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          CustombuttonWidget(
            text: 'Download Itinerary',
            backgroundColor: AppColors.primary,
            textColor: Colors.white,
            borderRadius: 8,
            height: 50,
            isFullWidth: true,
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Downloading itinerary...')),
              );
            },
            icon: const Icon(
              Icons.download,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(height: 12),
          CustombuttonWidget(
            text: 'Contact Hotel',
            backgroundColor: Colors.white,
            textColor: AppColors.primary,
            borderRadius: 8,
            height: 50,
            isFullWidth: true,
            borderSide: BorderSide(color: AppColors.primary),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Contacting hotel...')),
              );
            },
            icon: Icon(
              Icons.phone,
              color: AppColors.primary,
              size: 20,
            ),
          ),
          const SizedBox(height: 12),
          CustombuttonWidget(
            text: 'View on Map',
            backgroundColor: Colors.white,
            textColor: AppColors.primary,
            borderRadius: 8,
            height: 50,
            isFullWidth: true,
            borderSide: BorderSide(color: AppColors.primary),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Opening map...')),
              );
            },
            icon: Icon(
              Icons.map,
              color: AppColors.primary,
              size: 20,
            ),
          ),
          const SizedBox(height: 12),
          CustombuttonWidget(
            text: 'Cancel Booking',
            backgroundColor: Colors.red.shade50,
            textColor: Colors.red.shade700,
            borderRadius: 8,
            height: 50,
            isFullWidth: true,
            borderSide: BorderSide(color: Colors.red.shade300),
            onPressed: () {
              _showCancellationDialog(context);
            },
            icon: Icon(
              Icons.cancel_outlined,
              color: Colors.red.shade700,
              size: 20,
            ),
          ),
        ],
      ),
    );
  }

  void _showCancellationDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(
                Icons.warning_amber_rounded,
                color: Colors.red.shade600,
                size: 28,
              ),
              const SizedBox(width: 12),
              const Text(
                'Cancel Booking',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Are you sure you want to cancel this booking?',
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Cancellation Policy',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.orange.shade800,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Free cancellation until April 26, 2025 (48 hours before check-in). After that, cancellation will incur a fee equivalent to the first night\'s stay.',
                      style: TextStyle(
                        color: Colors.orange.shade700,
                        fontSize: 13,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Keep Booking',
                style: TextStyle(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _processCancellation(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.shade600,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Cancel Booking',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
            ),
          ],
        );
      },
    );
  }

  void _processCancellation(BuildContext context) {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    // Simulate cancellation processing
    Future.delayed(const Duration(seconds: 2), () {
      Navigator.of(context).pop(); // Close loading dialog

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Booking cancelled successfully'),
          backgroundColor: Colors.green.shade600,
          behavior: SnackBarBehavior.floating,
        ),
      );

      // Navigate back to home or bookings list
      Navigator.of(context).popUntil((route) => route.isFirst);
    });
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 16, 16, 0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primary.withAlpha(20),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: AppColors.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: AppTextStyles.subtitle1.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),

          // Section content
          Padding(
            padding: const EdgeInsets.all(16),
            child: child,
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceRow(String label, String amount, {bool isTotal = false, bool isDiscount = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            fontSize: isTotal ? 16 : 14,
          ),
        ),
        Text(
          amount,
          style: TextStyle(
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            fontSize: isTotal ? 16 : 14,
            color: isDiscount
                ? Colors.green.shade700
                : (isTotal ? AppColors.primary : Colors.black),
          ),
        ),
      ],
    );
  }

  Widget _buildInclusionItem(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: AppColors.primary.withAlpha(20),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.check_circle,
            color: AppColors.primary,
            size: 16,
          ),
          const SizedBox(width: 6),
          Text(
            text,
            style: TextStyle(
              fontSize: 12,
              color: AppColors.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmenityItem(IconData icon, String text) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: AppColors.primary.withAlpha(20),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: AppColors.primary,
            size: 16,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: const TextStyle(
              fontSize: 13,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActivityItem(String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: AppColors.accent.withAlpha(20),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.directions_run,
              color: AppColors.accent,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPolicyItem(String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: TextStyle(
              color: Colors.grey.shade700,
              fontSize: 13,
            ),
          ),
        ],
      ),
    );
  }
}
