
import 'package:flutter/material.dart';
import 'package:hotel_booking/routes/app_routes.dart';

// class AuthProvider with ChangeNotifier {
//   bool isLoading = false;
//   String? error;
//   bool isLoggedIn = false;
//   String? currentUserName;
//   String? currentUserEmail;

//   // Temporary storage for registration
//   String? tempUserName;
//   String? tempUserEmail;

//   // Login method
//   Future<void> login(
//       BuildContext context, String email, String password) async {
//     // Set loading state
//     isLoading = true;
//     error = null;
//     notifyListeners();

//     try {
//       // Simulate API call
//       await Future.delayed(const Duration(seconds: 2));

//       // For demo purposes, any email with a valid format and password length > 6 works
//       if (email.contains('@') && email.contains('.') && password.length >= 6) {
//         isLoggedIn = true;
//         currentUserEmail = email;
//         currentUserName = email.split('@')[0]; // Just for demo

//         // Check if context is still valid
//         if (context.mounted) {
//           // Navigate to home screen after successful login
//           Navigator.pushReplacementNamed(context, AppRoutes.home);
//         }
//       } else {
//         error = 'Invalid email or password';
//       }
//     } catch (e) {
//       error = 'An error occurred. Please try again.';
//     } finally {
//       isLoading = false;
//       notifyListeners();
//     }
//   }

//   // Signup method
//   Future<void> signup(
//       BuildContext context, String name, String email, String password) async {
//     // Set loading state
//     isLoading = true;
//     error = null;
//     notifyListeners();

//     try {
//       // Simulate API call
//       await Future.delayed(const Duration(seconds: 2));

//       // For demo purposes, any valid inputs will work
//       if (name.isNotEmpty &&
//           email.contains('@') &&
//           email.contains('.') &&
//           password.length >= 6) {

//         // Store user data temporarily
//         tempUserName = name;
//         tempUserEmail = email;

//         // Check if context is still valid
//         if (context.mounted) {
//           // Navigate to OTP verification screen using AppRoutes
//           AppRoutes.navigateToOtpVerification(
//             context,
//             email,
//             false, // isFromForgotPassword
//           );
//         }
//       } else {
//         error = 'Invalid inputs. Please check your information.';
//       }
//     } catch (e) {
//       error = 'An error occurred. Please try again.';
//     } finally {
//       isLoading = false;
//       notifyListeners();
//     }
//   }

//   // Complete registration after OTP verification
//   Future<void> completeRegistration(String name, String email) async {
//     isLoading = true;
//     error = null;
//     notifyListeners();

//     try {
//       // Simulate API call
//       await Future.delayed(const Duration(seconds: 1));

//       // Set user as logged in
//       isLoggedIn = true;
//       currentUserName = name;
//       currentUserEmail = email;
//     } catch (e) {
//       error = 'An error occurred. Please try again.';
//     } finally {
//       isLoading = false;
//       notifyListeners();
//     }
//   }

//   // Logout method
//   void logout() {
//     isLoggedIn = false;
//     currentUserName = null;
//     currentUserEmail = null;
//     notifyListeners();
//   }
// }


class AuthProvider with ChangeNotifier {
  bool isLoading = false;
  String? error;
  bool isLoggedIn = false;
  String? currentUserEmail;

  // Store the input temporarily for OTP verification
  String? _tempInput;

  // Send OTP to email/whatsapp
  Future<void> sendOTP(BuildContext context, String input) async {
    isLoading = true;
    error = null;
    notifyListeners();

    try {
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call

      if (input.isNotEmpty) {
        _tempInput = input; // Store input temporarily
        
        if (context.mounted) {
          // Navigate to OTP verification screen
          AppRoutes.navigateToOtpVerification(
            context,
            input,
            false, // isFromForgotPassword = false for login flow
          );
        }
      } else {
        error = 'Please enter email or WhatsApp number';
      }
    } catch (e) {
      error = 'An error occurred. Please try again.';
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  // Complete login after OTP verification
  Future<void> completeLoginAfterOTP(BuildContext context) async {
    isLoading = true;
    error = null;
    notifyListeners();

    try {
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call

      if (_tempInput != null) {
        isLoggedIn = true;
        currentUserEmail = _tempInput;
        _tempInput = null; // Clear temporary storage

        if (context.mounted) {
          Navigator.pushReplacementNamed(context, AppRoutes.home);
        }
      } else {
        error = 'Invalid session. Please try again.';
      }
    } catch (e) {
      error = 'Login failed. Please try again.';
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  // Google login (direct login without OTP)
  Future<void> loginWithGoogle(BuildContext context) async {
    isLoading = true;
    error = null;
    notifyListeners();

    try {
      await Future.delayed(const Duration(seconds: 2)); // Simulate Google login API call

      // Simulate successful Google login
      isLoggedIn = true;
      currentUserEmail = '<EMAIL>'; // This would come from Google auth

      if (context.mounted) {
        Navigator.pushReplacementNamed(context, AppRoutes.home);
      }
    } catch (e) {
      error = 'Google login failed. Please try again.';
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  void logout() {
    isLoggedIn = false;
    currentUserEmail = null;
    _tempInput = null;
    notifyListeners();
  }
}