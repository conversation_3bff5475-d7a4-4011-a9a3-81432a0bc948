class HotelReviews {
  final HotelReviewsData data;

  HotelReviews({required this.data});

  factory HotelReviews.fromJson(Map<String, dynamic> json) {
    return HotelReviews(
      data: HotelReviewsData.fromJson(json['data']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data.toJson(),
    };
  }
}

class HotelReviewsData {
  final bool moreResultsAvailable;
  final List<Review> reviewList;
  final bool fallbackReviews;

  HotelReviewsData({
    required this.moreResultsAvailable,
    required this.reviewList,
    required this.fallbackReviews,
  });

  factory HotelReviewsData.fromJson(Map<String, dynamic> json) {
    return HotelReviewsData(
      moreResultsAvailable: json['moreResultsAvailable'] ?? false,
      reviewList: (json['reviewList'] as List<dynamic>?)
              ?.map((review) => Review.fromJson(review))
              .toList() ??
          [],
      fallbackReviews: json['fallbackReviews'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'moreResultsAvailable': moreResultsAvailable,
      'reviewList': reviewList.map((review) => review.toJson()).toList(),
      'fallbackReviews': fallbackReviews,
    };
  }
}

class Review {
  final String title;
  final String category;
  final String? location;
  final String reviewComment;
  final String reviewDate;
  final String avgRating;
  final String? countryCode;
  final String reviewerName;

  Review({
    required this.title,
    required this.category,
    this.location,
    required this.reviewComment,
    required this.reviewDate,
    required this.avgRating,
    this.countryCode,
    required this.reviewerName,
  });

  factory Review.fromJson(Map<String, dynamic> json) {
    return Review(
      title: json['title'] ?? '',
      category: json['category'] ?? '',
      location: json['location'],
      reviewComment: json['reviewComment'] ?? '',
      reviewDate: json['reviewDate'] ?? '',
      avgRating: json['avgRating'] ?? '0.0',
      countryCode: json['countryCode'],
      reviewerName: json['reviewerName'] ?? 'Anonymous',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'category': category,
      'location': location,
      'reviewComment': reviewComment,
      'reviewDate': reviewDate,
      'avgRating': avgRating,
      'countryCode': countryCode,
      'reviewerName': reviewerName,
    };
  }

  // Helper method to get rating as double
  double get ratingAsDouble {
    return double.tryParse(avgRating) ?? 0.0;
  }

  // Helper method to get star count (out of 5)
  int get starCount {
    return (ratingAsDouble / 2).round().clamp(0, 5);
  }

  // Helper method to get reviewer initials
  String get reviewerInitials {
    if (reviewerName.isEmpty || reviewerName == 'Anonymous') {
      return 'AN';
    }
    List<String> names = reviewerName.split(' ');
    if (names.length >= 2) {
      return '${names[0][0]}${names[1][0]}'.toUpperCase();
    } else {
      return names[0].length >= 2 
          ? names[0].substring(0, 2).toUpperCase()
          : names[0].toUpperCase();
    }
  }

  // Helper method to get category icon
  String get categoryIcon {
    switch (category.toLowerCase()) {
      case 'couple':
        return '💑';
      case 'family':
        return '👨‍👩‍👧‍👦';
      case 'group':
        return '👥';
      case 'solo traveller':
        return '🧳';
      default:
        return '👤';
    }
  }
}
