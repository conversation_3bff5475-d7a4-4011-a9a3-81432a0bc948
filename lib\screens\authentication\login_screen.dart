  // import 'package:flutter/material.dart';
  // import 'package:hotel_booking/constants/app_colors.dart';
  // import 'package:hotel_booking/constants/app_images.dart';
  // import 'package:hotel_booking/constants/app_text_styles.dart';
  // import 'package:hotel_booking/providers/auth_provider.dart';
  // import 'package:hotel_booking/routes/app_routes.dart';
  // import 'package:hotel_booking/widgets/custombutton_widget.dart';
  // import 'package:provider/provider.dart';

  // class LoginScreen extends StatefulWidget {
  //   const LoginScreen({super.key});

  //   @override
  //   State<LoginScreen> createState() => _LoginScreenState();
  // }

  // class _LoginScreenState extends State<LoginScreen> {
  //   final _formKey = GlobalKey<FormState>();
  //   final _emailController = TextEditingController();

  //   @override
  //   void dispose() {
  //     _emailController.dispose();
  //     super.dispose();
  //   }

  //   @override
  //   Widget build(BuildContext context) {
  //     final screenHeight = MediaQuery.of(context).size.height;

  //     return Scaffold(
  //       backgroundColor: AppColors.background,
  //       body: Column(
  //         children: [
  //           Container(
  //             height: screenHeight * 0.3,
  //             width: double.infinity,
  //             decoration: BoxDecoration(
  //               image: DecorationImage(
  //                 image: AssetImage(AppImages.background),
  //                 fit: BoxFit.cover,
  //                 colorFilter: ColorFilter.mode(
  //                   Colors.black.withAlpha(128),
  //                   BlendMode.darken,
  //                 ),
  //               ),
  //             ),
  //             child: SafeArea(
  //               child: Padding(
  //                 padding: const EdgeInsets.symmetric(horizontal: 20.0),
  //                 child: Column(
  //                   crossAxisAlignment: CrossAxisAlignment.start,
  //                   children: [
  //                     const SizedBox(height: 20),
  //                     // Logo and app name
  //                     Row(
  //                       children: [
  //                         Container(
  //                           padding: const EdgeInsets.all(8),
  //                           decoration: BoxDecoration(
  //                             color: Colors.white.withAlpha(50),
  //                             borderRadius: BorderRadius.circular(12),
  //                           ),
  //                           child: Image.asset(
  //                             AppImages.logo,
  //                             height: 32,
  //                             width: 32,
  //                             color: Colors.white,
  //                           ),
  //                         ),
  //                         const SizedBox(width: 12),
  //                         Text(
  //                           'KindAli Travel & Tourism',
  //                           style: AppTextStyles.headline2.copyWith(
  //                             color: Colors.white,
  //                             fontSize: 20,
  //                           ),
  //                         ),
  //                       ],
  //                     ),

  //                     const Spacer(),

  //                     // Welcome text
  //                     Text(
  //                       'Welcome Back',
  //                       style: AppTextStyles.headline1.copyWith(
  //                         color: Colors.white,
  //                         fontSize: 28,
  //                         fontWeight: FontWeight.bold,
  //                       ),
  //                     ),
  //                     const SizedBox(height: 8),
  //                     Container(
  //                       width: 40,
  //                       height: 3,
  //                       decoration: BoxDecoration(
  //                         color: AppColors.accent,
  //                         borderRadius: BorderRadius.circular(1.5),
  //                       ),
  //                     ),
  //                     const SizedBox(height: 8),
  //                     Text(
  //                       'Sign in to continue',
  //                       style: TextStyle(
  //                         color: Colors.white.withAlpha(230),
  //                         fontSize: 16,
  //                       ),
  //                     ),
  //                     const SizedBox(height: 20),
  //                   ],
  //                 ),
  //               ),
  //             ),
  //           ),

  //           // Bottom container (70% of screen with rounded top corners)
  //           Expanded(
  //             child: Container(
  //               width: double.infinity,
  //               decoration: const BoxDecoration(
  //                 color: Colors.white,
  //                 borderRadius: BorderRadius.only(
  //                   topLeft: Radius.circular(30),
  //                   topRight: Radius.circular(30),
  //                 ),
  //                 boxShadow: [
  //                   BoxShadow(
  //                     color: Color(0x1A000000),
  //                     blurRadius: 10,
  //                     offset: Offset(0, -4),
  //                   ),
  //                 ],
  //               ),
  //               child: SingleChildScrollView(
  //                 physics: const BouncingScrollPhysics(),
  //                 child: Padding(
  //                   padding: const EdgeInsets.fromLTRB(24, 30, 24, 24),
  //                   child: Form(
  //                     key: _formKey,
  //                     child: Column(
  //                       crossAxisAlignment: CrossAxisAlignment.stretch,
  //                       children: [
  //                         // Email / WhatsApp field
  //                         Container(
  //                           decoration: BoxDecoration(
  //                             border: Border.all(color: AppColors.divider),
  //                             borderRadius: BorderRadius.circular(12),
  //                           ),
  //                           child: TextFormField(
  //                             controller: _emailController,
  //                             keyboardType: TextInputType.emailAddress,
  //                             decoration: InputDecoration(
  //                               labelText: 'Email / WhatsApp',
  //                               labelStyle: TextStyle(color: AppColors.textLight),
  //                               hintText: 'Enter your email or WhatsApp number',
  //                               prefixIcon: Icon(Icons.person_outline, color: AppColors.primary),
  //                               border: InputBorder.none,
  //                               contentPadding: const EdgeInsets.symmetric(
  //                                 vertical: 16,
  //                                 horizontal: 20,
  //                               ),
  //                             ),
  //                             validator: (value) {
  //                               if (value == null || value.isEmpty) {
  //                                 return 'Please enter your email or WhatsApp number';
  //                               }
  //                               return null;
  //                             },
  //                           ),
  //                         ),

  //                         const SizedBox(height: 20),

  //                         // Continue button (now sends OTP)
  //                         Consumer<AuthProvider>(
  //                           builder: (context, authProvider, _) {
  //                             return CustombuttonWidget(
  //                               text: 'Continue',
  //                               backgroundColor: AppColors.primary,
  //                               textColor: Colors.white,
  //                               borderRadius: 12,
  //                               height: 56,
  //                               isFullWidth: true,
  //                               isLoading: authProvider.isLoading,
  //                               onPressed: () {
  //                                 if (_formKey.currentState!.validate()) {
  //                                   // Send OTP instead of direct login
  //                                   authProvider.sendOTP(
  //                                     context,
  //                                     _emailController.text,
  //                                   );
  //                                 }
  //                               },
  //                               textStyle: AppTextStyles.button.copyWith(
  //                                 fontSize: 18,
  //                               ),
  //                             );
  //                           },
  //                         ),

  //                         const SizedBox(height: 20),

  //                         // Divider
  //                         Row(
  //                           mainAxisAlignment: MainAxisAlignment.center,
  //                           children: [
  //                             const SizedBox(width: 150, child: Divider()),
  //                             Text(
  //                               ' Or ',
  //                               style: TextStyle(
  //                                 color: AppColors.textLight,
  //                                 fontSize: 16,
  //                               ),
  //                             ),
  //                             const SizedBox(width: 150, child: Divider())
  //                           ],
  //                         ),
                          
  //                         const SizedBox(height: 20),

  //                         // Google login button
  //                         Consumer<AuthProvider>(
  //                           builder: (context, authProvider, _) {
  //                             return _socialLoginButton(
  //                               onPressed: authProvider.isLoading 
  //                                   ? () {} // Disable when loading
  //                                   : () {
  //                                       authProvider.loginWithGoogle(context);
  //                                     },
  //                               icon: Icons.g_mobiledata,
  //                               label: 'Continue with Google',
  //                               isLoading: authProvider.isLoading,
  //                             );
  //                           },
  //                         ),
  //                       ],
  //                     ),
  //                   ),
  //                 ),
  //               ),
  //             ),
  //           ),
  //         ],
  //       ),
  //     );
  //   }

  //   Widget _socialLoginButton({
  //     required VoidCallback onPressed,
  //     required IconData icon,
  //     required String label,
  //     bool isLoading = false,
  //   }) {
  //     return InkWell(
  //       onTap: isLoading ? null : onPressed,
  //       borderRadius: BorderRadius.circular(12),
  //       child: Container(
  //         padding: const EdgeInsets.symmetric(vertical: 12),
  //         decoration: BoxDecoration(
  //           border: Border.all(color: AppColors.divider),
  //           borderRadius: BorderRadius.circular(12),
  //           color: Colors.white,
  //         ),
  //         child: Row(
  //           mainAxisAlignment: MainAxisAlignment.center,
  //           children: [
  //             if (isLoading)
  //               SizedBox(
  //                 width: 24,
  //                 height: 24,
  //                 child: CircularProgressIndicator(
  //                   strokeWidth: 2,
  //                   valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
  //                 ),
  //               )
  //             else
  //               Icon(
  //                 icon,
  //                 size: 24,
  //                 color: AppColors.primary,
  //               ),
  //             const SizedBox(width: 8),
  //             Text(
  //               label,
  //               style: TextStyle(
  //                 color: AppColors.text,
  //                 fontWeight: FontWeight.w500,
  //               ),
  //             ),
  //           ],
  //         ),
  //       ),
  //     );
  //   }
  // }
  import 'package:flutter/material.dart';
import 'package:hotel_booking/constants/app_colors.dart';
import 'package:hotel_booking/constants/app_images.dart';
import 'package:hotel_booking/constants/app_text_styles.dart';
import 'package:hotel_booking/providers/auth_provider.dart';
import 'package:hotel_booking/routes/app_routes.dart';
import 'package:hotel_booking/widgets/custombutton_widget.dart';
import 'package:provider/provider.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _inputController = TextEditingController();
  
  // Selection state
  bool isWhatsAppSelected = true; // Default to WhatsApp
  String selectedCountryCode = '+91'; // Default country code
  
  // Country codes list (you can expand this)
  final List<Map<String, String>> countryCodes = [
    {'code': '+1', 'country': 'US', 'flag': '🇺🇸'},
    {'code': '+44', 'country': 'UK', 'flag': '🇬🇧'},
    {'code': '+91', 'country': 'IN', 'flag': '🇮🇳'},
    {'code': '+971', 'country': 'UAE', 'flag': '🇦🇪'},
    {'code': '+966', 'country': 'SA', 'flag': '🇸🇦'},
    {'code': '+974', 'country': 'QA', 'flag': '🇶🇦'},
    {'code': '+965', 'country': 'KW', 'flag': '🇰🇼'},
    {'code': '+973', 'country': 'BH', 'flag': '🇧🇭'},
    {'code': '+968', 'country': 'OM', 'flag': '🇴🇲'},
    {'code': '+20', 'country': 'EG', 'flag': '🇪🇬'},
  ];

  @override
  void dispose() {
    _inputController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;

    return Scaffold(
      backgroundColor: AppColors.background,
      body: Column(
        children: [
          Container(
            height: screenHeight * 0.3,
            width: double.infinity,
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(AppImages.background),
                fit: BoxFit.cover,
                colorFilter: ColorFilter.mode(
                  Colors.black.withAlpha(128),
                  BlendMode.darken,
                ),
              ),
            ),
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 20),
                    // Logo and app name
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white.withAlpha(50),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Image.asset(
                            AppImages.logo,
                            height: 32,
                            width: 32,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'KindAli Travel & Tourism',
                          style: AppTextStyles.headline2.copyWith(
                            color: Colors.white,
                            fontSize: 20,
                          ),
                        ),
                      ],
                    ),

                    const Spacer(),

                    // Welcome text
                    Text(
                      'Welcome Back',
                      style: AppTextStyles.headline1.copyWith(
                        color: Colors.white,
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: 40,
                      height: 3,
                      decoration: BoxDecoration(
                        color: AppColors.accent,
                        borderRadius: BorderRadius.circular(1.5),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Sign in to continue',
                      style: TextStyle(
                        color: Colors.white.withAlpha(230),
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ),

          // Bottom container (70% of screen with rounded top corners)
          Expanded(
            child: Container(
              width: double.infinity,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(30),
                  topRight: Radius.circular(30),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Color(0x1A000000),
                    blurRadius: 10,
                    offset: Offset(0, -4),
                  ),
                ],
              ),
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(24, 30, 24, 24),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Selection checkboxes
                        Row(
                          children: [
                            // WhatsApp selection
                            GestureDetector(
                              onTap: () {
                                setState(() {
                                  isWhatsAppSelected = true;
                                  _inputController.clear();
                                });
                              },
                              child: Row(
                                children: [
                                  Container(
                                    width: 20,
                                    height: 20,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: isWhatsAppSelected 
                                            ? AppColors.primary 
                                            : AppColors.textLight,
                                        width: 2,
                                      ),
                                      color: isWhatsAppSelected 
                                          ? AppColors.primary 
                                          : Colors.transparent,
                                    ),
                                    child: isWhatsAppSelected
                                        ? const Icon(
                                            Icons.check,
                                            size: 14,
                                            color: Colors.white,
                                          )
                                        : null,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'WhatsApp',
                                    style: TextStyle(
                                      color: isWhatsAppSelected 
                                          ? AppColors.primary 
                                          : AppColors.textLight,
                                      fontWeight: isWhatsAppSelected 
                                          ? FontWeight.w600 
                                          : FontWeight.normal,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            
                            const SizedBox(width: 30),
                            
                            // Email selection
                            GestureDetector(
                              onTap: () {
                                setState(() {
                                  isWhatsAppSelected = false;
                                  _inputController.clear();
                                });
                              },
                              child: Row(
                                children: [
                                  Container(
                                    width: 20,
                                    height: 20,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: !isWhatsAppSelected 
                                            ? AppColors.primary 
                                            : AppColors.textLight,
                                        width: 2,
                                      ),
                                      color: !isWhatsAppSelected 
                                          ? AppColors.primary 
                                          : Colors.transparent,
                                    ),
                                    child: !isWhatsAppSelected
                                        ? const Icon(
                                            Icons.check,
                                            size: 14,
                                            color: Colors.white,
                                          )
                                        : null,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Email',
                                    style: TextStyle(
                                      color: !isWhatsAppSelected 
                                          ? AppColors.primary 
                                          : AppColors.textLight,
                                      fontWeight: !isWhatsAppSelected 
                                          ? FontWeight.w600 
                                          : FontWeight.normal,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 20),

                        // Input field with country code for WhatsApp
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.divider),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: isWhatsAppSelected
                              ? Row(
                                  children: [
                                    // Country code selector for WhatsApp
                                    InkWell(
                                      onTap: _showCountryCodePicker,
                                      borderRadius: const BorderRadius.only(
                                        topLeft: Radius.circular(12),
                                        bottomLeft: Radius.circular(12),
                                      ),
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 16,
                                          vertical: 16,
                                        ),
                                        decoration: BoxDecoration(
                                          border: Border(
                                            right: BorderSide(
                                              color: AppColors.divider,
                                            ),
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Text(
                                              countryCodes.firstWhere(
                                                (country) => country['code'] == selectedCountryCode,
                                                orElse: () => countryCodes.first,
                                              )['flag']!,
                                              style: const TextStyle(fontSize: 16),
                                            ),
                                            const SizedBox(width: 8),
                                            Text(
                                              selectedCountryCode,
                                              style: TextStyle(
                                                color: AppColors.text,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                            const SizedBox(width: 4),
                                            Icon(
                                              Icons.keyboard_arrow_down,
                                              color: AppColors.textLight,
                                              size: 20,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    // WhatsApp number input
                                    Expanded(
                                      child: TextFormField(
                                        controller: _inputController,
                                        keyboardType: TextInputType.phone,
                                        decoration: InputDecoration(
                                          labelText: 'WhatsApp Number',
                                          labelStyle: TextStyle(color: AppColors.textLight),
                                          hintText: 'Enter your WhatsApp number',
                                          prefixIcon: Icon(
                                            Icons.phone_outlined,
                                            color: AppColors.primary,
                                          ),
                                          border: InputBorder.none,
                                          contentPadding: const EdgeInsets.symmetric(
                                            vertical: 16,
                                            horizontal: 16,
                                          ),
                                        ),
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return 'Please enter your WhatsApp number';
                                          }
                                          if (value.length < 7) {
                                            return 'Please enter a valid phone number';
                                          }
                                          return null;
                                        },
                                      ),
                                    ),
                                  ],
                                )
                              : 
                              // Email input field
                              TextFormField(
                                controller: _inputController,
                                keyboardType: TextInputType.emailAddress,
                                decoration: InputDecoration(
                                  labelText: 'Email',
                                  labelStyle: TextStyle(color: AppColors.textLight),
                                  hintText: 'Enter your email address',
                                  prefixIcon: Icon(
                                    Icons.email_outlined,
                                    color: AppColors.primary,
                                  ),
                                  border: InputBorder.none,
                                  contentPadding: const EdgeInsets.symmetric(
                                    vertical: 16,
                                    horizontal: 20,
                                  ),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please enter your email address';
                                  }
                                  if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                                    return 'Please enter a valid email address';
                                  }
                                  return null;
                                },
                              ),
                        ),

                        const SizedBox(height: 20),

                        // Continue button (now sends OTP)
                        Consumer<AuthProvider>(
                          builder: (context, authProvider, _) {
                            return CustombuttonWidget(
                              text: 'Continue',
                              backgroundColor: AppColors.primary,
                              textColor: Colors.white,
                              borderRadius: 12,
                              height: 56,
                              isFullWidth: true,
                              isLoading: authProvider.isLoading,
                              onPressed: () {
                                if (_formKey.currentState!.validate()) {
                                  String fullInput = isWhatsAppSelected
                                      ? '$selectedCountryCode${_inputController.text}'
                                      : _inputController.text;
                                  
                                  // Send OTP with full input (country code + number for WhatsApp)
                                  authProvider.sendOTP(
                                    context,
                                    fullInput,
                                  );
                                }
                              },
                              textStyle: AppTextStyles.button.copyWith(
                                fontSize: 18,
                              ),
                            );
                          },
                        ),

                        const SizedBox(height: 20),

                        // Divider
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const SizedBox(width: 100, child: Divider()),
                            Text(
                              ' Or ',
                              style: TextStyle(
                                color: AppColors.textLight,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(width: 100, child: Divider())
                          ],
                        ),
                        
                        const SizedBox(height: 20),

                        // Google login button
                        Consumer<AuthProvider>(
                          builder: (context, authProvider, _) {
                            return _socialLoginButton(
                              onPressed: authProvider.isLoading 
                                  ? () {} // Disable when loading
                                  : () {
                                      authProvider.loginWithGoogle(context);
                                    },
                              icon: Icons.g_mobiledata,
                              label: 'Continue with Google',
                              isLoading: authProvider.isLoading,
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showCountryCodePicker() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.6,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.only(top: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              
              // Header
              Padding(
                padding: const EdgeInsets.all(20),
                child: Text(
                  'Select Country Code',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.text,
                  ),
                ),
              ),
              
              // Country list
              Expanded(
                child: ListView.builder(
                  itemCount: countryCodes.length,
                  itemBuilder: (context, index) {
                    final country = countryCodes[index];
                    final isSelected = country['code'] == selectedCountryCode;
                    
                    return InkWell(
                      onTap: () {
                        setState(() {
                          selectedCountryCode = country['code']!;
                        });
                        Navigator.pop(context);
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 16,
                        ),
                        decoration: BoxDecoration(
                          color: isSelected 
                              ? AppColors.primary.withAlpha(20)
                              : Colors.transparent,
                        ),
                        child: Row(
                          children: [
                            Text(
                              country['flag']!,
                              style: const TextStyle(fontSize: 20),
                            ),
                            const SizedBox(width: 16),
                            Text(
                              country['country']!,
                              style: TextStyle(
                                color: AppColors.text,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const Spacer(),
                            Text(
                              country['code']!,
                              style: TextStyle(
                                color: isSelected 
                                    ? AppColors.primary 
                                    : AppColors.textLight,
                                fontWeight: isSelected 
                                    ? FontWeight.bold 
                                    : FontWeight.normal,
                              ),
                            ),
                            if (isSelected) ...[
                              const SizedBox(width: 8),
                              Icon(
                                Icons.check,
                                color: AppColors.primary,
                                size: 20,
                              ),
                            ],
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _socialLoginButton({
    required VoidCallback onPressed,
    required IconData icon,
    required String label,
    bool isLoading = false,
  }) {
    return InkWell(
      onTap: isLoading ? null : onPressed,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.divider),
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (isLoading)
              SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),
              )
            else
              Icon(
                icon,
                size: 24,
                color: AppColors.primary,
              ),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                color: AppColors.text,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}