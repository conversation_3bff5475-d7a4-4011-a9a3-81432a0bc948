// import 'package:flutter/material.dart';
// import 'package:hotel_booking/constants/app_colors.dart';
// import 'package:carousel_slider/carousel_slider.dart';
// import 'package:hotel_booking/models/hotel_details.dart';
// import 'package:hotel_booking/screens/booking%20screen/booking_screen.dart';

// class BookingSheetWidget extends StatefulWidget {
//   final InventoryInfoList? hotel;

//   const BookingSheetWidget({
//     super.key,
//     this.hotel,
//   });

//   @override
//   State<BookingSheetWidget> createState() => _BookingSheetWidgetState();
// }

// class _BookingSheetWidgetState extends State<BookingSheetWidget> {
//   // Track selected room and meal option
//   String selectedRoomType = 'Deluxe Room';
//   bool includeBreakfast = false;
//   int _currentImageIndex = 0;
//   final CarouselSliderController _carouselController =
//       CarouselSliderController();

//   // Room pricing data with image URLs
//   final Map<String, Map<String, dynamic>> roomData = {
//     'Ordinary Room': {
//       'description': 'Standard bed, Free WiFi',
//       'roomOnly': 1000,
//       'withBreakfast': 1200,
//       'images': [
//         'assets/images/bilderboken-rlwE8f8anOc-unsplash.jpg',
//         'assets/images/christian-lambert-vmIWr0NnpCQ-unsplash.jpg',
//         'assets/images/rakabtw_-M3YuHIpgmSY-unsplash.jpg',
//       ],
//     },
//     'Deluxe Room': {
//       'description': 'King size bed, Sea view, Free WiFi',
//       'roomOnly': 2624,
//       'withBreakfast': 2824,
//       'images': [
//         'assets/images/bilderboken-rlwE8f8anOc-unsplash.jpg',
//         'assets/images/christian-lambert-vmIWr0NnpCQ-unsplash.jpg',
//         'assets/images/rakabtw_-M3YuHIpgmSY-unsplash.jpg',
//       ],
//     },
//     'Premium Suite': {
//       'description': 'King size bed, Balcony, Premium amenities',
//       'roomOnly': 3845,
//       'withBreakfast': 4045,
//       'images': [
//         'assets/images/bilderboken-rlwE8f8anOc-unsplash.jpg',
//         'assets/images/christian-lambert-vmIWr0NnpCQ-unsplash.jpg',
//         'assets/images/rakabtw_-M3YuHIpgmSY-unsplash.jpg',
//       ],
//     },
//     'Family Room': {
//       'description': 'Two double beds, Garden view, Mini bar',
//       'roomOnly': 3124,
//       'withBreakfast': 3324,
//       'images': [
//         'assets/images/bilderboken-rlwE8f8anOc-unsplash.jpg',
//         'assets/images/christian-lambert-vmIWr0NnpCQ-unsplash.jpg',
//         'assets/images/rakabtw_-M3YuHIpgmSY-unsplash.jpg',
//       ],
//     },
//   };

//   // Amenities for room options
//   final List<String> roomOnlyAmenities = [
//     'Non-refundable',
//     'Parking',
//     'Welcome drink',
//     'Coffee & tea',
//     'Express check-in',
//     'Free WiFi',
//     'Drinking water',
//   ];

//   final List<String> roomWithBreakfastAmenities = [
//     'Non-refundable',
//     'Parking',
//     'Welcome drink',
//     'Coffee & tea',
//     'Express check-in',
//     'Free WiFi',
//     'Drinking water',
//     'Breakfast included',
//   ];

//   // Calculate total based on selection and stay duration
//   int calculateTotal() {
//     final int nightlyRate = includeBreakfast
//         ? roomData[selectedRoomType]!['withBreakfast']
//         : roomData[selectedRoomType]!['roomOnly'];
//     final int nights = 2; // Hardcoded for now, could be calculated from dates
//     final int roomCharge = nightlyRate * nights;
//     final int taxesAndFees = (roomCharge * 0.1).round(); // 10% tax
//     return roomCharge + taxesAndFees;
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Positioned(
//       bottom: 0,
//       left: 0,
//       right: 0,
//       child: Container(
//         padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
//         decoration: BoxDecoration(
//           color: Colors.white,
//           boxShadow: [
//             BoxShadow(
//               color: Colors.black.withOpacity(0.1),
//               blurRadius: 10,
//               offset: const Offset(0, -5),
//             ),
//           ],
//         ),
//         child: ElevatedButton(
//           onPressed: () {
//             _showBookingBottomSheet(context);
//           },
//           style: ElevatedButton.styleFrom(
//             backgroundColor: AppColors.primary,
//             padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
//             shape: RoundedRectangleBorder(
//               borderRadius: BorderRadius.circular(12),
//             ),
//           ),
//           child: const Text(
//             'Available Rooms',
//             style: TextStyle(
//                 fontSize: 16,
//                 fontWeight: FontWeight.bold,
//                 color: AppColors.background),
//           ),
//         ),
//       ),
//     );
//   }

//   void _showBookingBottomSheet(BuildContext context) {
//     showModalBottomSheet(
//       context: context,
//       isScrollControlled: true,
//       backgroundColor: Colors.transparent,
//       builder: (context) {
//         return StatefulBuilder(builder: (context, setModalState) {
//           return DraggableScrollableSheet(
//             initialChildSize: 0.8,
//             maxChildSize: 0.9,
//             minChildSize: 0.5,
//             builder: (context, scrollController) {
//               return Container(
//                 decoration: const BoxDecoration(
//                   color: Colors.white,
//                   borderRadius: BorderRadius.only(
//                     topLeft: Radius.circular(24),
//                     topRight: Radius.circular(24),
//                   ),
//                 ),
//                 child: Column(
//                   children: [
//                     // Drag handle
//                     Container(
//                       margin: const EdgeInsets.only(top: 10),
//                       width: 40,
//                       height: 5,
//                       decoration: BoxDecoration(
//                         color: Colors.grey[300],
//                         borderRadius: BorderRadius.circular(5),
//                       ),
//                     ),
//                     // Header
//                     Padding(
//                       padding: const EdgeInsets.all(20),
//                       child: Row(
//                         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                         children: [
//                           const Text(
//                             'Book Your Stay',
//                             style: TextStyle(
//                               fontSize: 22,
//                               fontWeight: FontWeight.bold,
//                             ),
//                           ),
//                           IconButton(
//                             onPressed: () => Navigator.pop(context),
//                             icon: Container(
//                               padding: const EdgeInsets.all(4),
//                               decoration: BoxDecoration(
//                                 color: Colors.grey[100],
//                                 shape: BoxShape.circle,
//                               ),
//                               child: const Icon(Icons.close),
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                     // Content
//                     Expanded(
//                       child: ListView(
//                         controller: scrollController,
//                         padding: const EdgeInsets.symmetric(horizontal: 20),
//                         children: [
//                           // Room selection
//                           _buildSectionTitle('Select Room Type'),
//                           const SizedBox(height: 12),

//                           // Room type options with meal choices
//                           ...roomData.keys
//                               .map((roomType) => Padding(
//                                     padding: const EdgeInsets.only(bottom: 12),
//                                     child: _buildRoomTypeOptionWithMeal(
//                                       roomType,
//                                       roomData[roomType]!['description'],
//                                       roomData[roomType]!['roomOnly'],
//                                       roomData[roomType]!['withBreakfast'],
//                                       selectedRoomType == roomType,
//                                       (selected) {
//                                         setModalState(() {
//                                           selectedRoomType = roomType;
//                                           _currentImageIndex = 0;
//                                           setState(() {
//                                             selectedRoomType = roomType;
//                                           });
//                                         });
//                                       },
//                                       includeBreakfast,
//                                       (value) {
//                                         setModalState(() {
//                                           includeBreakfast = value;
//                                           setState(() {
//                                             includeBreakfast = value;
//                                           });
//                                         });
//                                       },
//                                       setModalState,
//                                     ),
//                                   ))
//                               .toList(),

//                           const SizedBox(height: 24),

//                           // Price details
//                           _buildPriceDetails(setModalState),
//                           const SizedBox(height: 24),

//                           // Terms and conditions
//                           Row(
//                             children: [
//                               Icon(Icons.check_circle,
//                                   color: AppColors.primary, size: 20),
//                               const SizedBox(width: 8),
//                               Expanded(
//                                 child: Text(
//                                   'By booking, you agree to the Terms & Conditions and Cancellation Policy',
//                                   style: TextStyle(
//                                     fontSize: 12,
//                                     color: Colors.grey[600],
//                                   ),
//                                 ),
//                               ),
//                             ],
//                           ),
//                           const SizedBox(height: 24),
//                         ],
//                       ),
//                     ),
//                     // Bottom action button
//                     Container(
//                       padding: const EdgeInsets.all(20),
//                       decoration: BoxDecoration(
//                         color: Colors.white,
//                         boxShadow: [
//                           BoxShadow(
//                             color: Colors.black.withOpacity(0.05),
//                             blurRadius: 10,
//                             offset: const Offset(0, -5),
//                           ),
//                         ],
//                       ),
//                       child: SizedBox(
//                         width: double.infinity,
//                         child: ElevatedButton(
//                           onPressed: () {
//                             Navigator.push(
//                               context,
//                               MaterialPageRoute(
//                                 builder: (context) => BookingScreen(
//                                   hotel: widget.hotel,
//                                   roomType: selectedRoomType,
//                                   checkInDate: DateTime.now().add(const Duration(days: 1)),
//                                   checkOutDate: DateTime.now().add(const Duration(days: 3)),
//                                   adultCount: 2,
//                                   childCount: 0,
//                                 ),
//                               ),
//                             );
//                           },
//                           style: ElevatedButton.styleFrom(
//                             backgroundColor: AppColors.primary,
//                             padding: const EdgeInsets.symmetric(vertical: 16),
//                             shape: RoundedRectangleBorder(
//                               borderRadius: BorderRadius.circular(12),
//                             ),
//                           ),
//                           child: const Text(
//                             'Reserve Room',
//                             style: TextStyle(
//                               fontSize: 16,
//                               fontWeight: FontWeight.bold,
//                               color: Colors.white,
//                             ),
//                           ),
//                         ),
//                       ),
//                     ),
//                   ],
//                 ),
//               );
//             },
//           );
//         });
//       },
//     );
//   }

//   Widget _buildSectionTitle(String title) {
//     return Text(
//       title,
//       style: const TextStyle(
//         fontSize: 18,
//         fontWeight: FontWeight.bold,
//       ),
//     );
//   }

//   Widget _buildRoomTypeOptionWithMeal(
//     String title,
//     String description,
//     int roomOnlyPrice,
//     int withBreakfastPrice,
//     bool isSelected,
//     Function(bool) onSelected,
//     bool currentIncludeBreakfast,
//     Function(bool) onBreakfastToggle,
//     StateSetter setModalState,
//   ) {
//     return GestureDetector(
//       onTap: () => onSelected(true),
//       child: Container(
//         padding: const EdgeInsets.all(16),
//         decoration: BoxDecoration(
//           color: isSelected ? Colors.blue.withOpacity(0.1) : Colors.grey[100],
//           borderRadius: BorderRadius.circular(12),
//           border: isSelected
//               ? Border.all(color: AppColors.primary, width: 2)
//               : null,
//         ),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Row(
//               children: [
//                 Expanded(
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       Text(
//                         title,
//                         style: TextStyle(
//                           fontSize: 16,
//                           fontWeight: FontWeight.bold,
//                           color: isSelected ? AppColors.primary : Colors.black,
//                         ),
//                       ),
//                       const SizedBox(height: 4),
//                       Text(
//                         description,
//                         style: TextStyle(
//                           fontSize: 13,
//                           color: Colors.grey[600],
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//                 isSelected
//                     ? Icon(Icons.check_circle, color: AppColors.primary)
//                     : Icon(Icons.circle_outlined, color: Colors.grey[400]),
//               ],
//             ),
//             if (isSelected) const SizedBox(height: 12),

//             // Room Image Carousel for Selected Room
//             if (isSelected) ...[
//               _buildRoomImageCarousel(title, setModalState),
//               const SizedBox(height: 12),
//               const Divider(height: 1),
//             ],

//             if (isSelected)
//               Padding(
//                 padding: const EdgeInsets.only(top: 12),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Row(
//                       children: [
//                         Expanded(
//                           child: Column(
//                             children: [
//                               // Room Only Option
//                               Row(
//                                 children: [
//                                   Radio<bool>(
//                                     value: false,
//                                     groupValue: currentIncludeBreakfast,
//                                     onChanged: (value) =>
//                                         onBreakfastToggle(false),
//                                     activeColor: AppColors.primary,
//                                   ),
//                                   Expanded(
//                                     child: Column(
//                                       crossAxisAlignment:
//                                           CrossAxisAlignment.start,
//                                       children: [
//                                         const Text(
//                                           'Room Only',
//                                           style: TextStyle(
//                                             fontSize: 14,
//                                             fontWeight: FontWeight.w500,
//                                           ),
//                                         ),
//                                         Text(
//                                           '\$$roomOnlyPrice per night',
//                                           style: const TextStyle(
//                                             fontSize: 13,
//                                             color: AppColors.secondary,
//                                             fontWeight: FontWeight.bold,
//                                           ),
//                                         ),
//                                       ],
//                                     ),
//                                   ),
//                                 ],
//                               ),

//                               // Room Only Amenities
//                               if (!currentIncludeBreakfast)
//                                 Container(
//                                   margin: const EdgeInsets.only(
//                                       left: 32, top: 8, bottom: 12),
//                                   padding: const EdgeInsets.all(12),
//                                   decoration: BoxDecoration(
//                                     color: Colors.grey[50],
//                                     borderRadius: BorderRadius.circular(8),
//                                     border:
//                                         Border.all(color: Colors.grey.shade200),
//                                   ),
//                                   child: Column(
//                                     crossAxisAlignment:
//                                         CrossAxisAlignment.start,
//                                     children: [
//                                       const Text(
//                                         'Room Only',
//                                         style: TextStyle(
//                                           fontSize: 14,
//                                           fontWeight: FontWeight.bold,
//                                           color: Colors.black87,
//                                         ),
//                                       ),
//                                       const SizedBox(height: 8),
//                                       Wrap(
//                                         spacing: 8,
//                                         runSpacing: 8,
//                                         children: roomOnlyAmenities
//                                             .map((amenity) =>
//                                                 _buildAmenityChip(amenity))
//                                             .toList(),
//                                       ),
//                                     ],
//                                   ),
//                                 ),

//                               // Room with Breakfast Option
//                               Row(
//                                 children: [
//                                   Radio<bool>(
//                                     value: true,
//                                     groupValue: currentIncludeBreakfast,
//                                     onChanged: (value) =>
//                                         onBreakfastToggle(true),
//                                     activeColor: AppColors.primary,
//                                   ),
//                                   Expanded(
//                                     child: Column(
//                                       crossAxisAlignment:
//                                           CrossAxisAlignment.start,
//                                       children: [
//                                         const Text(
//                                           'Room with Breakfast',
//                                           style: TextStyle(
//                                             fontSize: 14,
//                                             fontWeight: FontWeight.w500,
//                                           ),
//                                         ),
//                                         Text(
//                                           '\$$withBreakfastPrice per night',
//                                           style: const TextStyle(
//                                             fontSize: 13,
//                                             color: AppColors.secondary,
//                                             fontWeight: FontWeight.bold,
//                                           ),
//                                         ),
//                                       ],
//                                     ),
//                                   ),
//                                 ],
//                               ),

//                               // Room with Breakfast Amenities
//                               if (currentIncludeBreakfast)
//                                 Container(
//                                   margin: const EdgeInsets.only(
//                                       left: 32, top: 8, bottom: 12),
//                                   padding: const EdgeInsets.all(12),
//                                   decoration: BoxDecoration(
//                                     color: Colors.grey[50],
//                                     borderRadius: BorderRadius.circular(8),
//                                     border:
//                                         Border.all(color: Colors.grey.shade200),
//                                   ),
//                                   child: Column(
//                                     crossAxisAlignment:
//                                         CrossAxisAlignment.start,
//                                     children: [
//                                       const Text(
//                                         'Room With Breakfast',
//                                         style: TextStyle(
//                                           fontSize: 14,
//                                           fontWeight: FontWeight.bold,
//                                           color: Colors.black87,
//                                         ),
//                                       ),
//                                       const SizedBox(height: 8),
//                                       Wrap(
//                                         spacing: 8,
//                                         runSpacing: 8,
//                                         children: roomWithBreakfastAmenities
//                                             .map((amenity) =>
//                                                 _buildAmenityChip(amenity))
//                                             .toList(),
//                                       ),
//                                     ],
//                                   ),
//                                 ),
//                             ],
//                           ),
//                         ),
//                       ],
//                     ),
//                   ],
//                 ),
//               ),
//           ],
//         ),
//       ),
//     );
//   }

//   Widget _buildRoomImageCarousel(String roomType, StateSetter setModalState) {
//     List<String> images = roomData[roomType]!['images'];

//     return Column(
//       children: [
//         CarouselSlider(
//           carouselController: _carouselController,
//           options: CarouselOptions(
//             height: 180,
//             viewportFraction: 1.0,
//             enlargeCenterPage: false,
//             enableInfiniteScroll: true,
//             onPageChanged: (index, reason) {
//               setModalState(() {
//                 _currentImageIndex = index;
//               });
//             },
//           ),
//           items: images.map((imageUrl) {
//             return Builder(
//               builder: (BuildContext context) {
//                 return Container(
//                   width: MediaQuery.of(context).size.width,
//                   margin: const EdgeInsets.symmetric(horizontal: 5.0),
//                   decoration: BoxDecoration(
//                     borderRadius: BorderRadius.circular(8),
//                     image: DecorationImage(
//                       image: AssetImage(imageUrl),
//                       fit: BoxFit.cover,
//                     ),
//                   ),
//                 );
//               },
//             );
//           }).toList(),
//         ),
//         const SizedBox(height: 8),

//         // Image indicators
//         Row(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: images.asMap().entries.map((entry) {
//             return GestureDetector(
//               onTap: () {
//                 _carouselController.animateToPage(entry.key);
//                 setModalState(() {
//                   _currentImageIndex = entry.key;
//                 });
//               },
//               child: Container(
//                 width: 8.0,
//                 height: 8.0,
//                 margin: const EdgeInsets.symmetric(horizontal: 4.0),
//                 decoration: BoxDecoration(
//                   shape: BoxShape.circle,
//                   color: _currentImageIndex == entry.key
//                       ? AppColors.primary
//                       : Colors.grey[300],
//                 ),
//               ),
//             );
//           }).toList(),
//         ),

//         // View all photos button
//         Padding(
//           padding: const EdgeInsets.only(top: 8),
//           child: GestureDetector(
//             onTap: () {
//               _showAllPhotos(context, roomType);
//             },
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.center,
//               children: [
//                 Icon(Icons.photo_library, size: 16, color: AppColors.primary),
//                 const SizedBox(width: 4),
//                 Text(
//                   'View All Photos',
//                   style: TextStyle(
//                     color: AppColors.primary,
//                     fontWeight: FontWeight.w500,
//                     fontSize: 14,
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ),
//       ],
//     );
//   }

//   void _showAllPhotos(BuildContext context, String roomType) {
//     List<String> images = roomData[roomType]!['images'];

//     showDialog(
//       context: context,
//       builder: (BuildContext context) {
//         return Dialog(
//           insetPadding: const EdgeInsets.all(16),
//           shape: RoundedRectangleBorder(
//             borderRadius: BorderRadius.circular(16),
//           ),
//           child: Column(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               Padding(
//                 padding: const EdgeInsets.all(16),
//                 child: Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     Text(
//                       '$roomType Photos',
//                       style: const TextStyle(
//                         fontWeight: FontWeight.bold,
//                         fontSize: 18,
//                       ),
//                     ),
//                     IconButton(
//                       icon: const Icon(Icons.close),
//                       onPressed: () => Navigator.pop(context),
//                     ),
//                   ],
//                 ),
//               ),
//               const Divider(height: 1),
//               Flexible(
//                 child: Container(
//                   constraints: BoxConstraints(
//                     maxHeight: MediaQuery.of(context).size.height * 0.7,
//                   ),
//                   child: ListView.builder(
//                     shrinkWrap: true,
//                     itemCount: images.length,
//                     itemBuilder: (context, index) {
//                       return Container(
//                         margin: const EdgeInsets.all(8),
//                         height: 200,
//                         decoration: BoxDecoration(
//                           borderRadius: BorderRadius.circular(8),
//                           image: DecorationImage(
//                             image: AssetImage(images[index]),
//                             fit: BoxFit.cover,
//                           ),
//                         ),
//                       );
//                     },
//                   ),
//                 ),
//               ),
//               const SizedBox(height: 16),
//             ],
//           ),
//         );
//       },
//     );
//   }

//   Widget _buildAmenityChip(String amenity) {
//     IconData iconData;

//     // Assign icons based on amenity type
//     if (amenity.contains('Non-refundable')) {
//       iconData = Icons.money_off;
//     } else if (amenity.contains('Parking')) {
//       iconData = Icons.local_parking;
//     } else if (amenity.contains('Welcome drink')) {
//       iconData = Icons.local_bar;
//     } else if (amenity.contains('Coffee & tea')) {
//       iconData = Icons.coffee;
//     } else if (amenity.contains('Express check-in')) {
//       iconData = Icons.login;
//     } else if (amenity.contains('Free WiFi')) {
//       iconData = Icons.wifi;
//     } else if (amenity.contains('Drinking water')) {
//       iconData = Icons.water_drop;
//     } else if (amenity.contains('Breakfast')) {
//       iconData = Icons.breakfast_dining;
//     } else {
//       iconData = Icons.check_circle_outline;
//     }

//     return Container(
//       padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(12),
//         border: Border.all(color: Colors.grey.shade200),
//       ),
//       child: Row(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           Icon(iconData, size: 14, color: AppColors.primary),
//           const SizedBox(width: 4),
//           Text(
//             amenity,
//             style: TextStyle(
//               fontSize: 12,
//               color: Colors.grey[800],
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildTextField(String label) {
//     return TextField(
//       decoration: InputDecoration(
//         labelText: label,
//         border: OutlineInputBorder(
//           borderRadius: BorderRadius.circular(12),
//           borderSide: BorderSide(color: Colors.grey[300]!),
//         ),
//         enabledBorder: OutlineInputBorder(
//           borderRadius: BorderRadius.circular(12),
//           borderSide: BorderSide(color: Colors.grey[300]!),
//         ),
//         focusedBorder: OutlineInputBorder(
//           borderRadius: BorderRadius.circular(12),
//           borderSide: BorderSide(color: AppColors.primary),
//         ),
//         floatingLabelBehavior: FloatingLabelBehavior.auto,
//         contentPadding:
//             const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
//       ),
//     );
//   }

//   Widget _buildPriceDetails(StateSetter setModalState) {
//     // Calculate prices based on current selections
//     final int nightlyRate = includeBreakfast
//         ? roomData[selectedRoomType]!['withBreakfast']
//         : roomData[selectedRoomType]!['roomOnly'];
//     final int nights = 2; // Hardcoded for now
//     final int roomCharge = nightlyRate * nights;
//     final int taxesAndFees = (roomCharge * 0.1).round(); // 10% tax
//     final int total = roomCharge + taxesAndFees;

//     return Container(
//       padding: const EdgeInsets.all(16),
//       decoration: BoxDecoration(
//         color: Colors.grey[100],
//         borderRadius: BorderRadius.circular(12),
//       ),
//       child: Column(
//         children: [
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               Text(
//                 'Room Charge ($nights nights)',
//                 style: TextStyle(
//                   fontSize: 14,
//                   color: Colors.grey[700],
//                 ),
//               ),
//               Text(
//                 '\$$roomCharge',
//                 style: const TextStyle(
//                   fontSize: 14,
//                   fontWeight: FontWeight.w500,
//                 ),
//               ),
//             ],
//           ),
//           if (includeBreakfast) ...[
//             const SizedBox(height: 10),
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 const Text(
//                   'Breakfast Included',
//                   style: TextStyle(
//                     fontSize: 14,
//                     color: Colors.green,
//                   ),
//                 ),
//                 Text(
//                   'Included',
//                   style: TextStyle(
//                     fontSize: 14,
//                     color: Colors.grey[600],
//                     fontStyle: FontStyle.italic,
//                   ),
//                 ),
//               ],
//             ),
//           ],
//           const SizedBox(height: 10),
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               Text(
//                 'Taxes & Fees',
//                 style: TextStyle(
//                   fontSize: 14,
//                   color: Colors.grey[700],
//                 ),
//               ),
//               Text(
//                 '\$$taxesAndFees',
//                 style: const TextStyle(
//                   fontSize: 14,
//                   fontWeight: FontWeight.w500,
//                 ),
//               ),
//             ],
//           ),
//           const SizedBox(height: 10),
//           const Divider(),
//           const SizedBox(height: 10),
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               const Text(
//                 'Total',
//                 style: TextStyle(
//                   fontSize: 16,
//                   fontWeight: FontWeight.bold,
//                 ),
//               ),
//               Text(
//                 '\$$total',
//                 style: const TextStyle(
//                   fontSize: 16,
//                   fontWeight: FontWeight.bold,
//                   color: AppColors.primary,
//                 ),
//               ),
//             ],
//           ),
//         ],
//       ),
//     );
//   }
// }
