class SearchCitiesData {
  final List<SearchCity> cities;
  final List<PopularPlace> popularPlaces;

  SearchCitiesData({
    required this.cities,
    required this.popularPlaces,
  });

  factory SearchCitiesData.fromJson(Map<String, dynamic> json) {
    return SearchCitiesData(
      cities: (json['cities'] as List<dynamic>)
          .map((cityJson) => SearchCity.fromJson(cityJson))
          .toList(),
      popularPlaces: (json['popularPlaces'] as List<dynamic>)
          .map((placeJson) => PopularPlace.fromJson(placeJson))
          .toList(),
    );
  }
}

class SearchCity {
  final String name;
  final int propertyCount;
  final String countryCode;
  final String country;

  SearchCity({
    required this.name,
    required this.propertyCount,
    required this.countryCode,
    required this.country,
  });

  factory SearchCity.fromJson(Map<String, dynamic> json) {
    return SearchCity(
      name: json['name'] ?? '',
      propertyCount: json['propertyCount'] ?? 0,
      countryCode: json['countryCode'] ?? '',
      country: json['country'] ?? '',
    );
  }

  // Helper method to get flag emoji from country code
  String get flagEmoji {
    switch (countryCode.toLowerCase()) {
      case 'in':
        return '🇮🇳';
      case 'ae':
        return '🇦🇪';
      case 'gb':
        return '🇬🇧';
      case 'fr':
        return '🇫🇷';
      case 'us':
        return '🇺🇸';
      case 'sg':
        return '🇸🇬';
      case 'th':
        return '🇹🇭';
      case 'jp':
        return '🇯🇵';
      case 'au':
        return '🇦🇺';
      case 'es':
        return '🇪🇸';
      case 'it':
        return '🇮🇹';
      case 'de':
        return '🇩🇪';
      case 'cn':
        return '🇨🇳';
      case 'br':
        return '🇧🇷';
      case 'ca':
        return '🇨🇦';
      case 'mx':
        return '🇲🇽';
      default:
        return '🌍';
    }
  }
}

class PopularPlace {
  final String name;
  final String city;
  final int propertyCount;
  final String countryCode;
  final String country;

  PopularPlace({
    required this.name,
    required this.city,
    required this.propertyCount,
    required this.countryCode,
    required this.country,
  });

  factory PopularPlace.fromJson(Map<String, dynamic> json) {
    return PopularPlace(
      name: json['name'] ?? '',
      city: json['city'] ?? '',
      propertyCount: json['propertyCount'] ?? 0,
      countryCode: json['countryCode'] ?? '',
      country: json['country'] ?? '',
    );
  }

  // Helper method to get flag emoji from country code
  String get flagEmoji {
    switch (countryCode.toLowerCase()) {
      case 'in':
        return '🇮🇳';
      case 'ae':
        return '🇦🇪';
      case 'gb':
        return '🇬🇧';
      case 'fr':
        return '🇫🇷';
      case 'us':
        return '🇺🇸';
      case 'sg':
        return '🇸🇬';
      case 'th':
        return '🇹🇭';
      case 'jp':
        return '🇯🇵';
      case 'au':
        return '🇦🇺';
      case 'es':
        return '🇪🇸';
      case 'it':
        return '🇮🇹';
      case 'de':
        return '🇩🇪';
      case 'cn':
        return '🇨🇳';
      case 'br':
        return '🇧🇷';
      case 'ca':
        return '🇨🇦';
      case 'mx':
        return '🇲🇽';
      default:
        return '🌍';
    }
  }
}
