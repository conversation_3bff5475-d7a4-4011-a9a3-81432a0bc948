import 'package:flutter/material.dart';
import 'package:hotel_booking/constants/app_images.dart';
import 'package:hotel_booking/screens/authentication/login_screen.dart';

class SplashScreen extends StatefulWidget {
  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: Duration(seconds: 3),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.5,
      end: 0.8,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Interval(0.0, 0.5, curve: Curves.easeIn),
    ));

    // Start animation
    _animationController.forward().then((_) {
      // Navigate to login screen after animation completes
      Future.delayed(Duration(milliseconds: 500), () {
        Navigator.of(context).pushReplacement(
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) => LoginScreen(),
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              return FadeTransition(opacity: animation, child: child);
            },
            transitionDuration: Duration(milliseconds: 800),
          ),
        );
      });
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFF5F5F0), // Light cream background
      body: Center(
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Opacity(
              opacity: _opacityAnimation.value,
              child: Transform.scale(
                scale: _scaleAnimation.value,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Logo container
                    Container(
                      width: 200,
                      height: 200,
                      child: Image.asset(AppImages.logo),
                    ),
                    SizedBox(height: 40),
                    // Loading indicator
                    SizedBox(
                      width: 40,
                      height: 40,
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF8B4513)),
                        strokeWidth: 3,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

class LogoPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Color(0xFF8B4513)
      ..style = PaintingStyle.fill;

    final strokePaint = Paint()
      ..color = Color(0xFF8B4513)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    // Draw stylized building towers
    // Main tower
    canvas.drawRect(
      Rect.fromLTWH(size.width * 0.4, size.height * 0.2, size.width * 0.2, size.height * 0.8),
      paint,
    );

    // Side towers
    canvas.drawRect(
      Rect.fromLTWH(size.width * 0.1, size.height * 0.4, size.width * 0.15, size.height * 0.6),
      paint,
    );

    canvas.drawRect(
      Rect.fromLTWH(size.width * 0.75, size.height * 0.4, size.width * 0.15, size.height * 0.6),
      paint,
    );

    // Add decorative elements
    final goldPaint = Paint()
      ..color = Color(0xFFDAA520)
      ..style = PaintingStyle.fill;

    // Small airplane icon
    canvas.drawCircle(
      Offset(size.width * 0.15, size.height * 0.15),
      3,
      goldPaint,
    );

    // Draw simple airplane path
    final pathPaint = Paint()
      ..color = Color(0xFFDAA520)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;

    final path = Path();
    path.moveTo(size.width * 0.1, size.height * 0.2);
    path.lineTo(size.width * 0.25, size.height * 0.1);
    canvas.drawPath(path, pathPaint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

