import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hotel_booking/models/hotel_rooms.dart';

class SelectedRoomOption {
  final Room room;
  final RoomOption roomOption;
  int quantity;

  SelectedRoomOption({
    required this.room,
    required this.roomOption,
    this.quantity = 1,
  });
}

class RoomSelectionProvider with ChangeNotifier{
  Hotelrooms? _hotelrooms;
  List<Room> _rooms = [];
  String _selectedSortOption = 'all';
  List<SelectedRoomOption> _selectedRoomOptions = [];
  int _maxRoomCount = 1; // Default to 1 room
  int _totalGuests = 2; // Default to 2 guests

  List<Room> get rooms => _rooms;
  Hotelrooms? get hotelrooms => _hotelrooms;
  String get selectedSortOption => _selectedSortOption;
  List<SelectedRoomOption> get selectedRoomOptions => _selectedRoomOptions;
  int get maxRoomCount => _maxRoomCount;
  int get totalGuests => _totalGuests;


  Future<void> loadHotelRoomsFromJson() async {
    try {
      final String jsonString = await rootBundle.loadString('assets/json/hotelleveldetails.json');
      _hotelrooms = hotelroomsFromJson(jsonString);
      _rooms = _hotelrooms?.data?.rooms ?? [];

      notifyListeners();
    } catch (e) {
      print('Failed to load hotel rooms: $e');
    }
  }

    void setSortOption(String option) {
    _selectedSortOption = option;
    notifyListeners();
  }

  // Set room limits from homescreen selection
  void setRoomLimits({required int maxRoomCount, required int totalGuests}) {
    _maxRoomCount = maxRoomCount;
    _totalGuests = totalGuests;
    notifyListeners();
  }

 List<RoomOption> getFilteredRoomOptions(Room room) {
    if (_selectedSortOption == 'all') {
      return room.roomOptions ?? [];
    }

    return (room.roomOptions ?? []).where((option) {
      switch (_selectedSortOption) {
        case 'free_breakfast':
          return option.mealBenefits?.any((benefit) =>
            benefit.toLowerCase().contains('breakfast')) ?? false;
        case 'free_parking_wifi_breakfast':
          return (option.mealBenefits?.any((benefit) =>
            benefit.toLowerCase().contains('breakfast')) ?? false) &&
            (option.otherBenefits?.any((benefit) =>
              benefit.toLowerCase().contains('parking') ||
              benefit.toLowerCase().contains('wifi')) ?? false);
        case 'breakfast':
          return option.mealBenefits?.any((benefit) =>
            benefit.toLowerCase().contains('breakfast')) ?? false;
        case 'bed_and_breakfast':
          return option.name?.toLowerCase().contains('bed') ?? false ||
            (option.mealBenefits?.any((benefit) =>
              benefit.toLowerCase().contains('breakfast')) ?? false);
        case 'free_parking_wifi_room':
          return option.otherBenefits?.any((benefit) =>
            benefit.toLowerCase().contains('parking') ||
            benefit.toLowerCase().contains('wifi')) ?? false;
        case 'room_only':
          return (option.mealBenefits?.isEmpty ?? true) ||
            (option.name?.toLowerCase().contains('room only') ?? false);
        default:
          return true;
      }
    }).toList();
  }

  // Get quantity for a specific room option
  int getRoomOptionQuantity(Room room, RoomOption roomOption) {
    final selectedOption = _selectedRoomOptions.firstWhere(
      (selected) => selected.room.id == room.id && selected.roomOption.blockId == roomOption.blockId,
      orElse: () => SelectedRoomOption(room: room, roomOption: roomOption, quantity: 0),
    );
    return selectedOption.quantity;
  }

  // Add room option
  void addRoomOption(Room room, RoomOption roomOption) {
    final existingIndex = _selectedRoomOptions.indexWhere(
      (selected) => selected.room.id == room.id && selected.roomOption.blockId == roomOption.blockId,
    );

    // Check if adding this room would exceed the maximum room count
    if (totalSelectedRooms >= _maxRoomCount && existingIndex == -1) {
      // Cannot add new room type, maximum room count reached
      return;
    }

    if (existingIndex != -1) {
      // Check if increasing quantity would exceed the maximum room count
      if (totalSelectedRooms >= _maxRoomCount) {
        return;
      }
      _selectedRoomOptions[existingIndex].quantity++;
    } else {
      _selectedRoomOptions.add(SelectedRoomOption(
        room: room,
        roomOption: roomOption,
        quantity: 1,
      ));
    }
    notifyListeners();
  }

  // Remove room option
  void removeRoomOption(Room room, RoomOption roomOption) {
    final existingIndex = _selectedRoomOptions.indexWhere(
      (selected) => selected.room.id == room.id && selected.roomOption.blockId == roomOption.blockId,
    );

    if (existingIndex != -1) {
      if (_selectedRoomOptions[existingIndex].quantity > 1) {
        _selectedRoomOptions[existingIndex].quantity--;
      } else {
        _selectedRoomOptions.removeAt(existingIndex);
      }
      notifyListeners();
    }
  }

  // Calculate total price
  double get totalPrice {
    double total = 0.0;
    for (final selectedOption in _selectedRoomOptions) {
      final price = selectedOption.roomOption.fareDetail?.totalPrice ?? 0.0;
      total += price * selectedOption.quantity;
    }
    return total;
  }

  // Get total selected rooms count
  int get totalSelectedRooms {
    return _selectedRoomOptions.fold(0, (sum, option) => sum + option.quantity);
  }

  // Clear all selections
  void clearSelections() {
    _selectedRoomOptions.clear();
    notifyListeners();
  }

  // Check if a room can be added (for UI feedback)
  bool canAddRoom(Room room, RoomOption roomOption) {
    final existingIndex = _selectedRoomOptions.indexWhere(
      (selected) => selected.room.id == room.id && selected.roomOption.blockId == roomOption.blockId,
    );

    if (existingIndex != -1) {
      // Can increase quantity if total rooms is less than max
      return totalSelectedRooms < _maxRoomCount;
    } else {
      // Can add new room type if total rooms is less than max
      return totalSelectedRooms < _maxRoomCount;
    }
  }

  // Get remaining room slots
  int get remainingRoomSlots => _maxRoomCount - totalSelectedRooms;

}