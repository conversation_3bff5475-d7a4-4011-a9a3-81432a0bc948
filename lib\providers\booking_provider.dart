import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hotel_booking/models/hotel_details.dart';
import 'package:hotel_booking/models/hotel_offers.dart';

class BookingProvider extends ChangeNotifier {
  Hoteloffers? _hotelOffers;
  List<Offer> _offers = [];

  Hoteloffers? get hotelOffers => _hotelOffers;
  List<Offer> get offers => _offers;

  // Form controllers
  final formKey = GlobalKey<FormState>();
  final firstNameController = TextEditingController();
  final lastNameController = TextEditingController();
  final emailController = TextEditingController();
  final phoneController = TextEditingController();

  // Selected values
  String _selectedTitle = 'Mr.';
  String _selectedCountryCode = '+91';
  String? _selectedNationality;

  // Coupon and pricing
  double _roomCharges = 360.0;
  final double _taxesAndFees = 40.0;
  String? _appliedCouponCode;
  double _couponDiscount = 0.0;

  // Special request
  String? _specialRequest;

  // Hotel and booking details
  InventoryInfoList? _hotel;
  String _roomType = 'Deluxe Room';
  DateTime? _checkInDate;
  DateTime? _checkOutDate;
  int _adultCount = 2;
  int _childCount = 0;

  // Loading state
  bool _isLoading = false;
  String? _error;

  // List of nationalities
  final List<String> _nationalities = [
    'Indian', 'American', 'British', 'Canadian', 'Australian',
    'Chinese', 'Japanese', 'German', 'French', 'Italian',
    'Spanish', 'Russian', 'Brazilian', 'Mexican', 'South African'
  ];

  // Getters
  String get selectedTitle => _selectedTitle;
  String get selectedCountryCode => _selectedCountryCode;
  String? get selectedNationality => _selectedNationality;
  double get roomCharges => _roomCharges;
  double get taxesAndFees => _taxesAndFees;
  String? get appliedCouponCode => _appliedCouponCode;
  double get couponDiscount => _couponDiscount;
  String? get specialRequest => _specialRequest;
  InventoryInfoList? get hotel => _hotel;
  String get roomType => _roomType;
  DateTime? get checkInDate => _checkInDate;
  DateTime? get checkOutDate => _checkOutDate;
  int get adultCount => _adultCount;
  int get childCount => _childCount;
  bool get isLoading => _isLoading;
  String? get error => _error;
  List<String> get nationalities => _nationalities;

  // Calculate total amount
  double get totalAmount => _roomCharges + _taxesAndFees - _couponDiscount;

  // Initialize booking with hotel data
  void initBooking({
    InventoryInfoList? hotel,
    String? roomType,
    DateTime? checkInDate,
    DateTime? checkOutDate,
    int adultCount = 2,
    int childCount = 0,
  }) {
    _hotel = hotel;
    _roomType = roomType ?? 'Deluxe Room';
    _checkInDate = checkInDate;
    _checkOutDate = checkOutDate;
    _adultCount = adultCount;
    _childCount = childCount;
    notifyListeners();
  }

  // Set selected title
  void setSelectedTitle(String title) {
    _selectedTitle = title;
    notifyListeners();
  }

  // Set selected country code
  void setSelectedCountryCode(String code) {
    _selectedCountryCode = code;
    notifyListeners();
  }

  // Set selected nationality
  void setSelectedNationality(String? nationality) {
    _selectedNationality = nationality;
    notifyListeners();
  }

  // Apply coupon code
  void applyCoupon(String code, double discount) {
    _appliedCouponCode = code;
    _couponDiscount = discount;
    notifyListeners();
  }

  // Remove coupon code
  void removeCoupon() {
    _appliedCouponCode = null;
    _couponDiscount = 0.0;
    notifyListeners();
  }

  // Set special request
  void setSpecialRequest(String? request) {
    _specialRequest = request;
    notifyListeners();
  }

  // Set room charges
  void setRoomCharges(double charges) {
    _roomCharges = charges;
    notifyListeners();
  }

  // Validate form
  bool validateForm() {
    return formKey.currentState?.validate() ?? false;
  }

  // Get guest full name
  String getGuestFullName() {
    return '$_selectedTitle ${firstNameController.text} ${lastNameController.text}';
  }

  // Get guest contact
  String getGuestContact() {
    return '$_selectedCountryCode ${phoneController.text}';
  }

  // Get guest email
  String getGuestEmail() {
    return emailController.text;
  }

  // Format date for display
  String formatDate(DateTime date) {
    return '${date.day} ${_getMonthName(date.month)}, ${date.year}';
  }

  // Get month name
  String _getMonthName(int month) {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return months[month - 1];
  }

  // Calculate number of nights
  int get numberOfNights {
    if (_checkInDate == null || _checkOutDate == null) return 1;
    return _checkOutDate!.difference(_checkInDate!).inDays;
  }

  // Clear name fields only
  void clearNameFields() {
    firstNameController.clear();
    lastNameController.clear();
    emailController.clear();
    phoneController.clear();
    _selectedTitle = 'Mr.';
    _selectedCountryCode = '+91';
    _selectedNationality = null;
    notifyListeners();
  }

  // Reset booking data
  void resetBooking() {
    firstNameController.clear();
    lastNameController.clear();
    emailController.clear();
    phoneController.clear();
    _selectedTitle = 'Mr.';
    _selectedCountryCode = '+91';
    _selectedNationality = null;
    _appliedCouponCode = null;
    _couponDiscount = 0.0;
    _specialRequest = null;
    notifyListeners();
  }

  @override
  void dispose() {
    firstNameController.dispose();
    lastNameController.dispose();
    emailController.dispose();
    phoneController.dispose();
    super.dispose();
  }

  Future<void> loadHotelOffers() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final String jsonString = await rootBundle.loadString('assets/json/hotelbookingoffers.json');
      _hotelOffers = hoteloffersFromJson(jsonString);

      // Extract offers list for easier access
      _offers = _hotelOffers?.data?.offers ?? [];

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to load hotel offers: $e';
      _isLoading = false;
      notifyListeners();
      print('Failed to load hotel offers: $e');
    }
  }

  // Get unique offers (removing duplicates based on coupon code)
  List<Offer> getUniqueOffers() {
    final Map<String, Offer> uniqueOffers = {};
    for (final offer in _offers) {
      if (offer.couponCode != null && !uniqueOffers.containsKey(offer.couponCode)) {
        uniqueOffers[offer.couponCode!] = offer;
      }
    }
    return uniqueOffers.values.toList();
  }

  // Find offer by coupon code
  Offer? findOfferByCode(String couponCode) {
    try {
      return _offers.firstWhere((offer) => offer.couponCode == couponCode);
    } catch (e) {
      return null;
    }
  }

  // Check if coupon is valid
  bool isCouponValid(String couponCode) {
    return findOfferByCode(couponCode) != null;
  }
}