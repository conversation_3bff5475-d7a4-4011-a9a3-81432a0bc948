import 'package:flutter/material.dart';
import 'package:hotel_booking/constants/app_colors.dart';
import 'package:hotel_booking/models/hotel_details.dart';
import 'package:hotel_booking/models/hotel_rooms.dart';
import 'package:hotel_booking/providers/room_selection_provider.dart';

class RoomOptionsWidget extends StatefulWidget {
  final InventoryInfoList? hotel;

  final RoomSelectionProvider roomSelectionProvider;
  final Room room;
  final RoomOption roomOption;

  const RoomOptionsWidget({
    super.key,
    required this.roomSelectionProvider,
    required this.room,
    required this.roomOption, this.hotel,
  });

  @override
  State<RoomOptionsWidget> createState() => _RoomOptionsWidgetState();
}

class _RoomOptionsWidgetState extends State<RoomOptionsWidget> {
   String selectedRoomType = 'Deluxe Room';
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 280,
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.only(right: 16),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.primary.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Room Option Name - Fixed parsing
          Text(
            widget.roomOption.name ?? 'Room Option',
            overflow: TextOverflow.visible,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),

          const SizedBox(height: 12),

          Wrap(
            spacing: 8,
            runSpacing: 6,
            children: _buildTags(),
          ),

          const SizedBox(height: 16),

          // Discount Badge - using actual data if available
          if (widget.roomOption.fareDetail?.markupDiscountPercent != null)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                border: Border.all(color: const Color(0xFFa18674)),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                '${widget.roomOption.fareDetail?.markupDiscountPercent}% off',
                style: const TextStyle(
                  color: Color(0xFFa18674),
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),

          const SizedBox(height: 12),

          // Pricing - using actual fare data
          if (widget.roomOption.fareDetail?.displayedBaseFare != null)
            Text(
              '₹${widget.roomOption.fareDetail!.displayedBaseFare}',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 13,
                decoration: TextDecoration.lineThrough,
              ),
            ),

          Text(
            '₹${widget.roomOption.fareDetail?.totalPrice?.toInt() ?? 'N/A'}',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),

          if (widget.roomOption.fareDetail?.taxesAndFees != null)
            Text(
              '+ ₹${widget.roomOption.fareDetail!.taxesAndFees!.toInt()} taxes',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),

          Text(
            'per night for 1 room',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),

          const SizedBox(height: 12),

          // Rooms left - using actual data
          Text(
            '${widget.room.roomsLeft ?? 0} rooms left',
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFFa18674),
              fontWeight: FontWeight.w500,
            ),
          ),

  const Spacer(),

          // Counter Controls
          _buildCounterControls(),
        ],
      ),
    );
  }

  List<Widget> _buildTags() {
    List<Widget> tags = [];

    // Cancellation benefits
    if (widget.roomOption.cancellationBenefits?.code != null) {
      String cancellationText = widget.roomOption.cancellationBenefits!.code == Code.NON_REFUNDABLE
          ? 'Non-refundable'
          : 'Refundable';

      tags.add(Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            widget.roomOption.cancellationBenefits!.code == Code.NON_REFUNDABLE
                ? Icons.info_outline
                : Icons.check_circle_outline,
            size: 12,
            color: widget.roomOption.cancellationBenefits!.code == Code.NON_REFUNDABLE
                ? Colors.red
                : Colors.green,
          ),
          const SizedBox(width: 4),
          Text(
            cancellationText,
            style: TextStyle(
              fontSize: 11,
              color: widget.roomOption.cancellationBenefits!.code == Code.NON_REFUNDABLE
                  ? Colors.red
                  : Colors.grey.shade700,
            ),
          ),
        ],
      ));
    }

    // Meal benefits
    if (widget.roomOption.mealBenefits != null && widget.roomOption.mealBenefits!.isNotEmpty) {
      for (String benefit in widget.roomOption.mealBenefits!) {
        tags.add(Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.check_circle_outline,
              size: 12,
              color: Colors.green,
            ),
            const SizedBox(width: 4),
            Text(
              benefit,
              style: TextStyle(
                fontSize: 11,
                color: Colors.grey.shade700,
              ),
            ),
          ],
        ));
      }
    }

    // Other benefits
    if (widget.roomOption.otherBenefits != null && widget.roomOption.otherBenefits!.isNotEmpty) {
      for (String benefit in widget.roomOption.otherBenefits!) {
        tags.add(Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.check_circle_outline,
              size: 12,
              color: Colors.green,
            ),
            const SizedBox(width: 4),
            Text(
              benefit,
              style: TextStyle(
                fontSize: 11,
                color: Colors.grey.shade700,
              ),
            ),
          ]),
        );
      }
    }

    return tags;
  }

  Widget _buildCounterControls() {
    final quantity = widget.roomSelectionProvider.getRoomOptionQuantity(widget.room, widget.roomOption);
    final canAdd = widget.roomSelectionProvider.canAddRoom(widget.room, widget.roomOption);
    final remainingSlots = widget.roomSelectionProvider.remainingRoomSlots;

    if (quantity == 0) {
      // Show "Add" button when no rooms selected
      return SizedBox(
        width: double.infinity,
        height: 44,
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: canAdd ? AppColors.primary : Colors.grey.shade400,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            elevation: 0,
          ),
          onPressed: canAdd ? () {
            widget.roomSelectionProvider.addRoomOption(widget.room, widget.roomOption);
          } : null,
          child: Text(
            canAdd ? 'Add' : 'Room Limit Reached',
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 14,
              color: Colors.white,
            ),
          ),
        ),
      );
    } else {
      // Show counter controls when rooms are selected
      return Container(
        height: 44,
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.primary),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            // Decrease button
            Expanded(
              child: InkWell(
                onTap: () {
                  widget.roomSelectionProvider.removeRoomOption(widget.room, widget.roomOption);
                },
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  bottomLeft: Radius.circular(8),
                ),
                child: Container(
                  height: double.infinity,
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(8),
                      bottomLeft: Radius.circular(8),
                    ),
                  ),
                  child: const Icon(
                    Icons.remove,
                    color: AppColors.primary,
                  ),
                ),
              ),
            ),
            // Quantity display
            Container(
              width: 60,
              height: double.infinity,
              decoration: BoxDecoration(
                color: AppColors.primary,
                border: Border.symmetric(
                  vertical: BorderSide(color: AppColors.primary),
                ),
              ),
              child: Center(
                child: Text(
                  quantity.toString(),
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            // Increase button
            Expanded(
              child: InkWell(
                onTap: canAdd ? () {
                  widget.roomSelectionProvider.addRoomOption(widget.room, widget.roomOption);
                } : null,
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(8),
                  bottomRight: Radius.circular(8),
                ),
                child: Container(
                  height: double.infinity,
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.only(
                      topRight: Radius.circular(8),
                      bottomRight: Radius.circular(8),
                    ),
                  ),
                  child: Icon(
                    Icons.add,
                    color: canAdd ? AppColors.primary : Colors.grey.shade400,
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    }
  }
}