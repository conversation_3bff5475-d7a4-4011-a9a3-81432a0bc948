import 'package:flutter/material.dart';
import 'package:hotel_booking/constants/app_colors.dart';
import 'package:hotel_booking/constants/app_text_styles.dart';
import 'package:hotel_booking/models/hotel_details.dart';
import 'package:hotel_booking/providers/booking_provider.dart';
import 'package:hotel_booking/providers/room_selection_provider.dart';
import 'package:hotel_booking/screens/payment%20interface/payment_screen.dart';
import 'package:hotel_booking/screens/booking%20screen/widgets/discount_coupons_page_widget.dart';
import 'package:hotel_booking/screens/booking%20screen/widgets/cancellation_policy_widget.dart';
import 'package:hotel_booking/screens/booking%20screen/widgets/special_request_widget.dart';
import 'package:hotel_booking/widgets/custombutton_widget.dart';
import 'package:provider/provider.dart';

class BookingScreen extends StatefulWidget {
  final InventoryInfoList? hotel;
  final String? roomType;
  final DateTime? checkInDate;
  final DateTime? checkOutDate;
  final int adultCount;
  final int childCount;
  final List<SelectedRoomOption>? selectedRoomOptions;
  final double? totalPrice;

  const BookingScreen({
    super.key,
    this.hotel,
    this.roomType,
    this.checkInDate,
    this.checkOutDate,
    this.adultCount = 2,
    this.childCount = 0,
    this.selectedRoomOptions,
    this.totalPrice,
  });

  @override
  State<BookingScreen> createState() => _BookingScreenState();
}

class _BookingScreenState extends State<BookingScreen> {
  late BookingProvider _bookingProvider;

  @override
  void initState() {
    super.initState();
    // Initialize the provider
    _bookingProvider = BookingProvider();


    // Initialize booking data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Calculate room charges from selected room options
      double roomCharges = widget.totalPrice ?? 0.0;
      String roomType = widget.roomType ?? 'Selected Rooms';

      // If we have selected room options, use the first room's name as room type
      if (widget.selectedRoomOptions != null && widget.selectedRoomOptions!.isNotEmpty) {
        roomType = widget.selectedRoomOptions!.first.room.name ?? 'Selected Room';
      }

      _bookingProvider.initBooking(
        hotel: widget.hotel,
        roomType: roomType,
        checkInDate: widget.checkInDate,
        checkOutDate: widget.checkOutDate,
        adultCount: widget.adultCount,
        childCount: widget.childCount,
      );

      // Set the room charges from selected rooms
      if (roomCharges > 0) {
        _bookingProvider.setRoomCharges(roomCharges);
      }
    });
     Provider.of<BookingProvider>(context, listen: false).loadHotelOffers();
  }

  @override
  void dispose() {
    _bookingProvider.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<BookingProvider>(
      builder: (context, provider, child) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('Book Your Stay'),
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
            elevation: 0,
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(
                bottom: Radius.circular(20),
              ),
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.discount),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => DiscountCouponsPageWidget(bookingProvider: provider,
                        totalAmount: provider.roomCharges + provider.taxesAndFees,
                        onCouponApplied: (code, discount) {
                          provider.applyCoupon(code, discount);
                        },
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
          body: SingleChildScrollView(
            child: Column(
              children: [
                // Hotel summary card
                _buildHotelSummaryCard(provider),

                // Guest details form
                _buildGuestDetailsForm(provider),

                // Cancellation policy
                CancellationPolicyWidget(
                  checkInDate: provider.checkInDate != null
                      ? provider.formatDate(provider.checkInDate!)
                      : 'May 15, 2023',
                  checkInTime: '2:00 PM',
                ),

                // Special request
                SpecialRequestWidget(
                  onRequestSubmitted: (request) {
                    provider.setSpecialRequest(request);
                  },
                ),

                // Bottom action buttons
                _buildBottomActionButtons(provider),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHotelSummaryCard(BookingProvider provider) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Hotel image
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.asset(
                  'assets/images/sara-dubler-Koei_7yYtIo-unsplash.jpg',
                  width: 80,
                  height: 80,
                  fit: BoxFit.cover,
                ),
              ),
              const SizedBox(width: 12),

              // Hotel details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      provider.hotel?.name ?? '',
                      style: AppTextStyles.headline3,
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        const Icon(Icons.location_on, size: 16, color: AppColors.textLight),
                        const SizedBox(width: 4),
                        Text(
                          provider.hotel?.locality!= null
                              ? '${provider.hotel?.locality}, ${provider.hotel?.city ?? ''}'
                              : 'Dubai, UAE',
                          style: TextStyle(color: AppColors.textLight),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        const Icon(Icons.star, size: 16, color: AppColors.accent),
                        const SizedBox(width: 4),
                        Text(
                          provider.hotel?.userRating != null
                              ? '${provider.hotel?.userRating} (${provider.hotel?.userRatingCount ?? 0} reviews)'
                              : '4.8 (243 reviews)',
                          style: TextStyle(color: AppColors.textLight),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),

          const Divider(height: 24),

          // Booking details
          Row(
            children: [
              _buildBookingDetailItem(
                Icons.calendar_today,
                'Check-in',
                provider.checkInDate != null
                    ? provider.formatDate(provider.checkInDate!)
                    : 'May 15, 2023',
              ),
              _buildBookingDetailItem(
                Icons.calendar_today,
                'Check-out',
                provider.checkOutDate != null
                    ? provider.formatDate(provider.checkOutDate!)
                    : 'May 18, 2023',
              ),
              _buildBookingDetailItem(
                Icons.person,
                'Guests',
                '${provider.adultCount} ${provider.adultCount == 1 ? 'Adult' : 'Adults'}${provider.childCount > 0 ? ', ${provider.childCount} ${provider.childCount == 1 ? 'Child' : 'Children'}' : ''}',
              ),
            ],
          ),

          const Divider(height: 24),

          // Room details
          _buildRoomDetails(provider),
        ],
      ),
    );
  }

  Widget _buildBookingDetailItem(IconData icon, String title, String value) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: AppColors.primary),
              const SizedBox(width: 4),
              Text(
                title,
                style: TextStyle(
                  color: AppColors.textLight,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGuestDetailsForm(BookingProvider provider) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Form(
        key: provider.formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Enter Your Details',
              style: AppTextStyles.headline3.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),

            // Title selection
            Row(
              children: [
                _buildTitleOption(provider, 'Mr.'),
                const SizedBox(width: 16),
                _buildTitleOption(provider, 'Mrs.'),
                const SizedBox(width: 16),
                _buildTitleOption(provider, 'Miss.'),
              ],
            ),
            const SizedBox(height: 20),

            // First name and last name
            Row(
              children: [
                Expanded(
                  child: _buildTextField(
                    controller: provider.firstNameController,
                    labelText: 'First Name',
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your first name';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildTextField(
                    controller: provider.lastNameController,
                    labelText: 'Last Name',
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your last name';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Email address
            _buildTextField(
              controller: provider.emailController,
              labelText: 'Email Address',
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your email address';
                } else if (!value.contains('@') || !value.contains('.')) {
                  return 'Please enter a valid email address';
                }
                return null;
              },
            ),
            const SizedBox(height: 20),

            // Phone number with country code
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Country code dropdown
                Container(
                  height: 56,
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      value: provider.selectedCountryCode,
                      icon: Icon(Icons.keyboard_arrow_down, color: Colors.grey.shade700),
                      style: const TextStyle(
                        color: Colors.black,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                      items: ['+91', '+1', '+44', '+61', '+86'].map((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(value),
                        );
                      }).toList(),
                      onChanged: (String? newValue) {
                        if (newValue != null) {
                          provider.setSelectedCountryCode(newValue);
                        }
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 12),

                // Phone number field
                Expanded(
                  child: _buildTextField(
                    controller: provider.phoneController,
                    labelText: 'Mobile No',
                    keyboardType: TextInputType.phone,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your phone number';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Nationality dropdown
            Container(
              height: 56,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  isExpanded: true,
                  hint: Text(
                    'Nationality',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 16,
                    ),
                  ),
                  value: provider.selectedNationality,
                  icon: Icon(Icons.keyboard_arrow_down, color: Colors.grey.shade700),
                  style: const TextStyle(
                    color: Colors.black,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  items: provider.nationalities.map((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                  onChanged: (String? newValue) {
                    if (newValue != null) {
                      provider.setSelectedNationality(newValue);
                    }
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTitleOption(BookingProvider provider, String title) {
    final isSelected = provider.selectedTitle == title;

    return GestureDetector(
      onTap: () {
        provider.setSelectedTitle(title);
      },
      child: Row(
        children: [
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: isSelected ? AppColors.primary : Colors.grey.shade400,
                width: 1.5,
              ),
            ),
            child: isSelected
                ? Container(
                    margin: const EdgeInsets.all(3),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isSelected ? AppColors.primary : Colors.transparent,
                    ),
                  )
                : null,
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: TextStyle(
              color: isSelected ? AppColors.primary : Colors.grey.shade700,
              fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
              fontSize: 15,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String labelText,
    TextInputType keyboardType = TextInputType.text,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      style: const TextStyle(fontSize: 16),
      decoration: InputDecoration(
        labelText: labelText,
        labelStyle: TextStyle(
          color: Colors.grey.shade600,
          fontSize: 16,
        ),
        filled: false,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.primary, width: 1.5),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.red.shade300),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      ),
      validator: validator,
    );
  }

  Widget _buildBottomActionButtons(BookingProvider provider) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Price summary
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(13),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                _buildPriceRow(
                  'Room Charges (${provider.numberOfNights} nights)',
                  '₹${_getActualRoomCharges().toStringAsFixed(2)}'
                ),
                const SizedBox(height: 8),
                _buildPriceRow(
                  'Taxes & Fees',
                  '₹${provider.taxesAndFees.toStringAsFixed(2)}'
                ),

                // Coupon discount row
                if (provider.couponDiscount > 0) ...[
                  const SizedBox(height: 8),
                  _buildPriceRow(
                    'Coupon Discount',
                    '-₹${provider.couponDiscount.toStringAsFixed(2)}',
                    isDiscount: true,
                  ),
                ],

                // Add coupon option
                const SizedBox(height: 8),
                GestureDetector(
                  onTap: () {
                    _showCouponsPage(provider);
                  },
                  child: Row(
                    children: [
                      Icon(
                        Icons.local_offer_outlined,
                        size: 18,
                        color: AppColors.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        provider.appliedCouponCode != null
                            ? 'Coupon Applied: ${provider.appliedCouponCode}'
                            : 'Apply Coupon',
                        style: TextStyle(
                          color: AppColors.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const Spacer(),
                      Icon(
                        Icons.chevron_right,
                        size: 18,
                        color: AppColors.primary,
                      ),
                    ],
                  ),
                ),

                const Divider(height: 16),
                _buildPriceRow(
                  'Total Amount',
                  '₹${_getActualTotalAmount().toStringAsFixed(2)}',
                  isTotal: true,
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),

          // Continue button
          CustombuttonWidget(
            text: 'Continue to Payment',
            backgroundColor: AppColors.primary,
            textColor: Colors.white,
            borderRadius: 8,
            height: 56,
            isFullWidth: true,
            onPressed: () {
              if (provider.validateForm()) {
                // Navigate to payment screen
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => PaymentScreen(
                      totalAmount: _getActualTotalAmount(),
                      hotelName: provider.hotel?.name ?? 'Luxury Resort & Spa',
                      roomType: provider.roomType,
                      checkInDate: provider.checkInDate != null
                          ? provider.formatDate(provider.checkInDate!)
                          : 'May 15, 2023',
                      checkOutDate: provider.checkOutDate != null
                          ? provider.formatDate(provider.checkOutDate!)
                          : 'May 18, 2023',
                    ),
                  ),
                );
              }
            },
          ),
        ],
      ),
    );
  }

  void _showCouponsPage(BookingProvider provider) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DiscountCouponsPageWidget(bookingProvider: provider,
          totalAmount: _getActualRoomCharges() + provider.taxesAndFees,
          onCouponApplied: (code, discount) {
            provider.applyCoupon(code, discount);
          },
        ),
      ),
    );
  }

  // Get actual room charges from selected room options or fallback to provider
  double _getActualRoomCharges() {
    if (widget.totalPrice != null && widget.totalPrice! > 0) {
      return widget.totalPrice!;
    }
    return Provider.of<BookingProvider>(context, listen: false).roomCharges;
  }

  // Get actual total amount including taxes and discounts
  double _getActualTotalAmount() {
    final provider = Provider.of<BookingProvider>(context, listen: false);
    return _getActualRoomCharges() + provider.taxesAndFees - provider.couponDiscount;
  }

  Widget _buildPriceRow(String label, String amount, {bool isTotal = false, bool isDiscount = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            fontSize: isTotal ? 16 : 14,
          ),
        ),
        Text(
          amount,
          style: TextStyle(
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            fontSize: isTotal ? 16 : 14,
            color: isDiscount
                ? Colors.green.shade700
                : (isTotal ? AppColors.primary : AppColors.text),
          ),
        ),
      ],
    );
  }

  Widget _buildRoomDetails(BookingProvider provider) {
    if (widget.selectedRoomOptions != null && widget.selectedRoomOptions!.isNotEmpty) {
      // Show selected room options
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Selected Rooms (${widget.selectedRoomOptions!.fold(0, (sum, option) => sum + option.quantity)})',
            style: AppTextStyles.subtitle1.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          ...widget.selectedRoomOptions!.map((selectedOption) =>
            Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.neutralLight,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          selectedOption.room.name ?? 'Room',
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                        Text(
                          selectedOption.roomOption.name ?? 'Option',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        if (selectedOption.roomOption.mealBenefits != null &&
                            selectedOption.roomOption.mealBenefits!.isNotEmpty)
                          Text(
                            selectedOption.roomOption.mealBenefits!.join(', '),
                            style: TextStyle(
                              fontSize: 11,
                              color: Colors.green.shade700,
                            ),
                          ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        'Qty: ${selectedOption.quantity}',
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 12,
                        ),
                      ),
                      Text(
                        '₹${((selectedOption.roomOption.fareDetail?.totalPrice ?? 0.0) * selectedOption.quantity).toInt()}',
                        style: AppTextStyles.subtitle1.copyWith(
                          color: AppColors.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total Room Charges',
                style: AppTextStyles.subtitle1.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '₹${widget.totalPrice?.toInt() ?? 0}',
                style: AppTextStyles.subtitle1.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      );
    } else {
      // Show default room details
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                provider.roomType,
                style: AppTextStyles.subtitle1,
              ),
              Text(
                '\$${(provider.roomCharges / provider.numberOfNights).toStringAsFixed(0)}/night',
                style: AppTextStyles.subtitle1.copyWith(
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            'Breakfast included',
            style: TextStyle(color: AppColors.textLight),
          ),
        ],
      );
    }
  }
}