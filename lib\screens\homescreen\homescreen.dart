import 'package:flutter/material.dart';
import 'dart:ui';
import 'package:hotel_booking/constants/app_colors.dart';
import 'package:hotel_booking/constants/app_images.dart';
import 'package:hotel_booking/constants/app_localizations.dart';
import 'package:hotel_booking/helper%20functions/string_extention_helper.dart';
import 'package:hotel_booking/providers/home_provider.dart';
import 'package:hotel_booking/providers/localization_provider.dart';
import 'package:hotel_booking/screens/homescreen/widgets/date_selection_widget.dart';
import 'package:hotel_booking/screens/homescreen/widgets/guest_selection_widget.dart';
import 'package:hotel_booking/screens/hotel%20list%20screen/hotel_list_screen.dart';
import 'package:hotel_booking/screens/language%20screen/widget/language_bottomsheet.dart';
import 'package:hotel_booking/screens/profile%20screen/profile_screen.dart';
import 'package:hotel_booking/widgets/glow_loader_widget.dart';
import 'package:hotel_booking/widgets/ArcAvatarLoader_widget.dart';
import 'package:provider/provider.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
// Use addPostFrameCallback to ensure we're not in the build phase
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = Provider.of<HomeProvider>(context, listen: false);

// Load user data
      provider.loadUsers();

// Set default check-in date (today) and check-out date (tomorrow)
      if (provider.checkInDate == null) {
        provider.setCheckInDate(DateTime.now());
      }
      if (provider.checkOutDate == null) {
        provider.setCheckOutDate(DateTime.now().add(const Duration(days: 1)));
      }
    });
  }

  Widget _buildOfferStrip(
      String title, String subtitle, Color color, IconData icon) {
    return Container(
      width: double.infinity,
      height: 55, // Further reduced height as requested
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color,
            color.withAlpha(200),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: color.withAlpha(100),
            blurRadius: 10,
            spreadRadius: 1,
            offset: Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: Colors.white.withAlpha(30),
          width: 0.5,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Stack(
          children: [
            // Subtle pattern overlay for luxury feel
            Opacity(
              opacity: 0.05,
              child: Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: NetworkImage(
                        'https://www.transparenttextures.com/patterns/cubes.png'),
                    repeat: ImageRepeat.repeat,
                  ),
                ),
              ),
            ),

            // Shine effect
            Positioned(
              top: -20,
              left: -20,
              child: Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      Colors.white.withAlpha(50),
                      Colors.white.withAlpha(0),
                    ],
                  ),
                ),
              ),
            ),

            // Main content
            Row(
              children: [
                Container(
                  width: 55,
                  height: 55,
                  decoration: BoxDecoration(
                    color: Colors.white.withAlpha(40),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(16),
                      bottomLeft: Radius.circular(16),
                    ),
                  ),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      // Decorative circle behind icon
                      Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white.withAlpha(30),
                        ),
                      ),
                      // Icon
                      Icon(
                        icon,
                        color: Colors.white,
                        size: 26, // Reduced icon size
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16, // Reduced font size
                          fontWeight: FontWeight.bold,
                          letterSpacing: 0.5,
                          shadows: [
                            Shadow(
                              color: Colors.black.withAlpha(100),
                              blurRadius: 2,
                              offset: Offset(1, 1),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 2), // Reduced spacing
                      Text(
                        subtitle,
                        style: TextStyle(
                          color: Colors.white.withAlpha(230),
                          fontSize: 12, // Reduced font size
                        ),
                      ),
                    ],
                  ),
                ),
              
                Container(
                  margin: EdgeInsets.only(right: 10),
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withAlpha(40),
                    border: Border.all(
                      color: Colors.white.withAlpha(60),
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.white,
                    size: 14,
                  ),
                ),SizedBox(width: 10)
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to build luxury tour package cards
  Widget _buildTourPackage(String name, String location, String price,
      String originalPrice, String discount, String imageUrl) {
    return Container(
      width: double.infinity,
      height: 200, // Slightly taller for more luxury feel
      margin: EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(50),
            blurRadius: 15,
            spreadRadius: 2,
            offset: Offset(0, 8),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Background image with glass morphism effect
          ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: Stack(
              children: [
                // Base image
                Image.network(
                  imageUrl,
                  width: double.infinity,
                  height: 200,
                  fit: BoxFit.cover,
                ),

                // Luxury gradient overlay
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.black.withAlpha(0),
                        Colors.black.withAlpha(150),
                      ],
                      stops: [0.4, 1.0],
                    ),
                  ),
                ),

                // Subtle pattern overlay for luxury feel
                Opacity(
                  opacity: 0.03,
                  child: Container(
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: NetworkImage(
                            'https://www.transparenttextures.com/patterns/black-linen.png'),
                        repeat: ImageRepeat.repeat,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Gold accent line at top
          Positioned(
            top: 0,
            left: 20,
            right: 20,
            child: Container(
              height: 3,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: [
                    Colors.transparent,
                    Colors.amber.shade300,
                    Colors.amber.shade600,
                    Colors.amber.shade300,
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),

          // Luxury discount badge
          Positioned(
            top: 16,
            left: 16,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.orange.shade600,
                    Colors.orange.shade800,
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(50),
                    blurRadius: 4,
                    offset: Offset(0, 2),
                  ),
                ],
                border: Border.all(
                  color: Colors.white.withAlpha(40),
                  width: 0.5,
                ),
              ),
              child: Text(
                discount,
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                  letterSpacing: 0.5,
                ),
              ),
            ),
          ),

          // Package name and location with improved typography
          Positioned(
            left: 16,
            bottom: 50,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 0.5,
                    shadows: [
                      Shadow(
                        color: Colors.black.withAlpha(150),
                        blurRadius: 3,
                        offset: Offset(1, 1),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 6),
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(30),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.location_on,
                        color: Colors.white,
                        size: 12,
                      ),
                    ),
                    SizedBox(width: 6),
                    Text(
                      location,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        letterSpacing: 0.5,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Price with original price strikethrough
          Positioned(
            left: 16,
            bottom: 16,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  price,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 0.5,
                  ),
                ),
                SizedBox(width: 2),
                Text(
                  '/night',
                  style: TextStyle(
                    color: Colors.white.withAlpha(200),
                    fontSize: 12,
                  ),
                ),
                SizedBox(width: 8),
                Text(
                  originalPrice,
                  style: TextStyle(
                    color: Colors.white.withAlpha(160),
                    fontSize: 14,
                    decoration: TextDecoration.lineThrough,
                  ),
                ),
              ],
            ),
          ),

          // Luxury rating badge
          Positioned(
            right: 16,
            bottom: 16,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.black.withAlpha(100),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Colors.amber.shade300,
                  width: 0.5,
                ),
              ),
              child: Row(
                children: [
                  Text(
                    '4.6',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                  SizedBox(width: 4),
                  Icon(
                    Icons.star,
                    color: Colors.amber,
                    size: 12,
                  ),
                ],
              ),
            ),
          ),

          // Elegant navigation button
          Positioned(
            right: 16,
            top: 16,
            child: Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                color: Colors.white.withAlpha(30),
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white.withAlpha(50),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(30),
                    blurRadius: 4,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Icon(
                Icons.arrow_forward_ios,
                color: Colors.white,
                size: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build luxury destination cards
  Widget _buildDestinationCard(String name, String imageUrl) {
    return Container(
      width: 160, // Slightly wider for more presence
      margin: const EdgeInsets.only(right: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20), // More rounded corners
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(40),
            blurRadius: 12,
            spreadRadius: 2,
            offset: Offset(0, 6),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Stack(
          fit: StackFit.expand,
          children: [
            // Destination image
            Image.network(
              imageUrl,
              fit: BoxFit.cover,
            ),

            // Luxury gradient overlay
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withAlpha(180),
                  ],
                  stops: [
                    0.6,
                    1.0
                  ], // Gradient starts lower for a more premium look
                ),
              ),
            ),

            // Gold accent line at the bottom for luxury feel
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                height: 3,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                    colors: [
                      Colors.amber.shade200,
                      Colors.amber.shade600,
                      Colors.amber.shade200,
                    ],
                  ),
                ),
              ),
            ),

            // Destination name with improved typography
            Positioned(
              bottom: 14,
              left: 14,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      letterSpacing:
                          0.5, // Improved letter spacing for luxury feel
                      shadows: [
                        Shadow(
                          color: Colors.black.withAlpha(150),
                          blurRadius: 3,
                          offset: Offset(1, 1),
                        ),
                      ],
                    ),
                  ),
                  // Small indicator line for visual interest
                  Container(
                    margin: EdgeInsets.only(top: 4),
                    width: 30,
                    height: 2,
                    decoration: BoxDecoration(
                      color: Colors.amber.shade300,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<HomeProvider>(
      builder: (context, homeprovider, child) {
        return Consumer<LocalizationProvider>(
            builder: (context, localizationoprovider, child) {
          final localizations = AppLocalizations.of(context)!;
          return Scaffold(
              backgroundColor: AppColors.background,

              // Make the entire screen scrollable
              body: SingleChildScrollView(
                child: Column(
                  children: [
                    // SizedBox to hold the image and provide positioning context
                    SizedBox(
                      height: 400,
                      width: double.infinity,
                      child: Stack(
                        clipBehavior: Clip
                            .none, // Allow children to be rendered outside the stack bounds
                        children: [
                          // Image with rounded bottom corners and luxury gradient overlay
                          Stack(
                            children: [
                              // Base image
                              Container(
                                height: 400,
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  borderRadius: const BorderRadius.only(
                                    bottomLeft: Radius.circular(20),
                                    bottomRight: Radius.circular(20),
                                  ),
                                  image: DecorationImage(
                                    image: const NetworkImage(
                                        'https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1760&q=80'),
                                    fit: BoxFit.cover,
                                    colorFilter: ColorFilter.mode(
                                      const Color.fromRGBO(0, 0, 0, 0.2),
                                      BlendMode.darken,
                                    ),
                                  ),
                                  boxShadow: const [
                                    BoxShadow(
                                      color: Color.fromRGBO(0, 0, 0, 0.3),
                                      blurRadius: 15,
                                      offset: Offset(0, 5),
                                    ),
                                  ],
                                ),
                              ),
                              // Luxury gradient overlay
                              Container(
                                height: 400,
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  borderRadius: const BorderRadius.only(
                                    bottomLeft: Radius.circular(20),
                                    bottomRight: Radius.circular(20),
                                  ),
                                  gradient: const LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    colors: [
                                      Colors.transparent,
                                      Color.fromRGBO(0, 0, 0, 0.4),
                                    ],
                                  ),
                                ),
                              ),
                              // Subtle pattern overlay for luxury feel
                              Opacity(
                                opacity: 0.05,
                                child: Container(
                                  height: 400,
                                  width: double.infinity,
                                  decoration: const BoxDecoration(
                                    borderRadius: BorderRadius.only(
                                      bottomLeft: Radius.circular(20),
                                      bottomRight: Radius.circular(20),
                                    ),
                                    image: DecorationImage(
                                      image: NetworkImage(
                                          'https://www.transparenttextures.com/patterns/diamond-upholstery.png'),
                                      repeat: ImageRepeat.repeat,
                                    ),
                                  ),
                                ),
                              ),
                              // Logo and name at the top
                              SafeArea(
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 20),
                                  child: Row(
                                    children: [
                                      // Logo
                                      Container(
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          shape: BoxShape.circle,
                                          boxShadow: const [
                                            BoxShadow(
                                              color:
                                                  Color.fromRGBO(0, 0, 0, 0.1),
                                              blurRadius: 8,
                                              spreadRadius: 1,
                                              offset: Offset(0, 2),
                                            ),
                                          ],
                                        ),
                                        padding: const EdgeInsets.all(2),
                                        child: CircleAvatar(
                                          backgroundColor: Colors.white,
                                          radius: 20,
                                          backgroundImage:
                                              AssetImage(AppImages.logo),
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      // App name
                                      Column(
                                        mainAxisSize: MainAxisSize.min,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: const [
                                          Text(
                                            'KindAli',
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontWeight: FontWeight.bold,
                                              fontSize: 18,
                                              shadows: [
                                                Shadow(
                                                  color: Color.fromRGBO(
                                                      0, 0, 0, 0.5),
                                                  blurRadius: 3,
                                                  offset: Offset(0, 1),
                                                ),
                                              ],
                                            ),
                                          ),
                                          Text(
                                            'Travel & Tourism',
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 12,
                                              shadows: [
                                                Shadow(
                                                  color: Color.fromRGBO(
                                                      0, 0, 0, 0.5),
                                                  blurRadius: 3,
                                                  offset: Offset(0, 1),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                      const Spacer(),
                                      Container(
                                        decoration: const BoxDecoration(
                                          color: Color.fromRGBO(
                                              255, 255, 255, 0.2),
                                          shape: BoxShape.circle,
                                        ),
                                        child: IconButton(
                                          icon: const Icon(
                                            Icons.translate_rounded,
                                            color: Colors.white,
                                          ),
                                          onPressed: () {
                                            showModalBottomSheet(
                                              context: context,
                                              backgroundColor:
                                                  Colors.transparent,
                                              isScrollControlled: true,
                                              builder: (_) =>
                                                  LanguageBottomsheet(),
                                            );
                                          },
                                        ),
                                      ),
                                      SizedBox(
                                        width: 20,
                                      ),
                                      // Notification icon
                                      Container(
                                        decoration: const BoxDecoration(
                                          color: Color.fromRGBO(
                                              255, 255, 255, 0.2),
                                          shape: BoxShape.circle,
                                        ),
                                        child: IconButton(
                                          icon: const Icon(
                                            Icons.person_outline,
                                            color: Colors.white,
                                          ),
                                          onPressed: () {
                                            Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                builder: (context) =>
                                                    const ProfileScreen(),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),

                          // Container positioned half on and half off the image
                          Positioned(
                            bottom:
                                20, // Negative value to position it partially outside the image
                            left: 20,
                            right: 20,
                            child: Stack(
                              clipBehavior: Clip.none,
                              children: [
                                // Main container with glass effect
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(20),
                                  child: BackdropFilter(
                                    filter: ImageFilter.blur(
                                        sigmaX: 10, sigmaY: 10),
                                    child: Container(
                                      padding: EdgeInsets.all(16),
                                      decoration: BoxDecoration(
                                        color: Colors.white
                                            .withAlpha(51), // 0.2 * 255 = ~51
                                        borderRadius: BorderRadius.circular(20),
                                        border: Border.all(
                                          color: Colors.white
                                              .withAlpha(51), // 0.2 * 255 = ~51
                                          width: 1.5,
                                        ),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black.withAlpha(30),
                                            blurRadius: 10,
                                            spreadRadius: 2,
                                            offset: Offset(0, 4),
                                          ),
                                        ],
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          // Search field
                                          Container(
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: Colors.black.withAlpha(
                                                        26), // 0.1 * 255 = ~26
                                                    blurRadius: 8,
                                                    spreadRadius: 1,
                                                  ),
                                                ],
                                              ),
                                              child: TextField(
                                       keyboardType:
                                                        TextInputType.text,
                                                    decoration: InputDecoration(
                                                      hintText:
                                                          'search.destination_placeholder'
                                                              .tr,
                                                      prefixIcon: Icon(
                                                          Icons
                                                              .location_on_outlined,
                                                          color: AppColors
                                                              .primary),
                                                   
                                                      border:
                                                          OutlineInputBorder(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(12),
                                                        borderSide:
                                                            BorderSide.none,
                                                      ),
                                                      filled: true,
                                                      fillColor:
                                                          AppColors.background,
                                                      contentPadding:
                                                          const EdgeInsets
                                                              .symmetric(
                                                        vertical: 16,
                                                        horizontal: 20,
                                                      ),
                                                      floatingLabelBehavior:
                                                          FloatingLabelBehavior
                                                              .auto,
                                                      focusedBorder:
                                                          OutlineInputBorder(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(12),
                                                        borderSide: BorderSide(
                                                            color: AppColors
                                                                .primary,
                                                            width: 1.5),
                                                      ),
                                                    ),
                                                  )),

                                          const SizedBox(height: 12),

                                          // Date and guest selection row
                                          Row(
                                            children: [
                                              // Check-in and Check-out container
                                              Expanded(
                                                flex: 1,
                                                child: InkWell(
                                                  onTap: () {
                                                    showModalBottomSheet(
                                                      backgroundColor:
                                                          AppColors.primary,
                                                      context: context,
                                                      isScrollControlled: true,
                                                      constraints:
                                                          BoxConstraints(
                                                        minHeight:
                                                            MediaQuery.of(
                                                                        context)
                                                                    .size
                                                                    .height *
                                                                0.3,
                                                        maxHeight:
                                                            MediaQuery.of(
                                                                        context)
                                                                    .size
                                                                    .height *
                                                                0.7,
                                                      ),
                                                      shape:
                                                          const RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius.vertical(
                                                                top: Radius
                                                                    .circular(
                                                                        20)),
                                                      ),
                                                      builder: (context) {
                                                        return const DateSelectionWidget();
                                                      },
                                                    );
                                                  },
                                                  child: Container(
                                                    height: 60, // Fixed height
                                                    padding: EdgeInsets.all(10),
                                                    decoration: BoxDecoration(
                                                      color: Colors.white,
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              12),
                                                      border: homeprovider
                                                                  .checkInDate !=
                                                              null
                                                          ? Border.all(
                                                              color: const Color
                                                                  .fromRGBO(177, 145,
                                                                  118, 0.7),
                                                              width: 1.5)
                                                          : Border.all(
                                                              color: const Color
                                                                  .fromRGBO(
                                                                  128,
                                                                  128,
                                                                  128,
                                                                  0.3),
                                                              width: 1),
                                                      boxShadow: [
                                                        if (homeprovider
                                                                .checkInDate !=
                                                            null)
                                                          const BoxShadow(
                                                            color:
                                                                Color.fromRGBO(
                                                                    177,
                                                                    145,
                                                                    118,
                                                                    0.1),
                                                            blurRadius: 8,
                                                            spreadRadius: 2,
                                                          ),
                                                      ],
                                                    ),
                                                    child: Row(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      children: [
                                                        Icon(
                                                          Icons
                                                              .calendar_today_outlined,
                                                          size: 16,
                                                          color: homeprovider
                                                                      .checkInDate !=
                                                                  null
                                                              ? AppColors
                                                                  .primary
                                                              : Colors.grey,
                                                        ),
                                                        const SizedBox(
                                                            width: 4),
                                                        Expanded(
                                                          child: Row(
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .spaceEvenly,
                                                            children: [
                                                              Flexible(
                                                                child: Column(
                                                                  mainAxisAlignment:
                                                                      MainAxisAlignment
                                                                          .center,
                                                                  crossAxisAlignment:
                                                                      CrossAxisAlignment
                                                                          .start,
                                                                  children: [
                                                                    Text(
                                                                      'search.checkin_header'
                                                                          .tr,
                                                                      style:
                                                                          TextStyle(
                                                                        color: homeprovider.checkInDate !=
                                                                                null
                                                                            ? AppColors.primary
                                                                            : Colors.grey[600],
                                                                        fontSize:
                                                                            10,
                                                                      ),
                                                                      overflow:
                                                                          TextOverflow
                                                                              .ellipsis,
                                                                    ),
                                                                    Text(
                                                                      homeprovider.checkInDate !=
                                                                              null
                                                                          ? '${homeprovider.checkInDate!.day}/${homeprovider.checkInDate!.month}/${homeprovider.checkInDate!.year}'
                                                                          : 'Select date',
                                                                      style:
                                                                          TextStyle(
                                                                        fontWeight: homeprovider.checkInDate !=
                                                                                null
                                                                            ? FontWeight.bold
                                                                            : FontWeight.normal,
                                                                        color: homeprovider.checkInDate !=
                                                                                null
                                                                            ? AppColors.text
                                                                            : Colors.grey,
                                                                        fontSize:
                                                                            10,
                                                                      ),
                                                                      overflow:
                                                                          TextOverflow
                                                                              .ellipsis,
                                                                    ),
                                                                  ],
                                                                ),
                                                              ),
                                                              SizedBox(
                                                                height: 30,
                                                                child:
                                                                    VerticalDivider(
                                                                  color: const Color(
                                                                      0xFF7BBCB6),
                                                                  thickness: 1,
                                                                  width: 10,
                                                                ),
                                                              ),
                                                              Flexible(
                                                                child: Column(
                                                                  mainAxisAlignment:
                                                                      MainAxisAlignment
                                                                          .center,
                                                                  crossAxisAlignment:
                                                                      CrossAxisAlignment
                                                                          .start,
                                                                  children: [
                                                                    Text(
                                                                      'search.checkout_header'
                                                                          .tr,
                                                                      style:
                                                                          TextStyle(
                                                                        color: homeprovider.checkInDate !=
                                                                                null
                                                                            ? AppColors.primary
                                                                            : Colors.grey[600],
                                                                        fontSize:
                                                                            10,
                                                                      ),
                                                                      overflow:
                                                                          TextOverflow
                                                                              .ellipsis,
                                                                    ),
                                                                    Text(
                                                                      homeprovider.checkOutDate !=
                                                                              null
                                                                          ? '${homeprovider.checkOutDate!.day}/${homeprovider.checkOutDate!.month}/${homeprovider.checkOutDate!.year}'
                                                                          : 'Select date',
                                                                      style:
                                                                          TextStyle(
                                                                        fontWeight: homeprovider.checkOutDate !=
                                                                                null
                                                                            ? FontWeight.bold
                                                                            : FontWeight.normal,
                                                                        color: homeprovider.checkOutDate !=
                                                                                null
                                                                            ? AppColors.text
                                                                            : Colors.grey,
                                                                        fontSize:
                                                                            10,
                                                                      ),
                                                                      overflow:
                                                                          TextOverflow
                                                                              .ellipsis,
                                                                    ),
                                                                  ],
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),

                                              const SizedBox(width: 8),

                                              // Guests selection
                                              Expanded(
                                                flex: 1,
                                                child: InkWell(
                                                  onTap: () {
                                                    showModalBottomSheet(
                                                      backgroundColor:
                                                          AppColors.primary,
                                                      context: context,
                                                      isScrollControlled: true,
                                                      constraints:
                                                          BoxConstraints(
                                                        minHeight:
                                                            MediaQuery.of(
                                                                        context)
                                                                    .size
                                                                    .height *
                                                                0.3,
                                                        maxHeight:
                                                            MediaQuery.of(
                                                                        context)
                                                                    .size
                                                                    .height *
                                                                0.7,
                                                      ),
                                                      shape:
                                                          const RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius.vertical(
                                                                top: Radius
                                                                    .circular(
                                                                        20)),
                                                      ),
                                                      builder: (context) {
                                                        return const GuestSelectionWidget();
                                                      },
                                                    );
                                                  },
                                                  child: Container(
                                                    height:
                                                        60, // Fixed height to match date container
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            vertical: 10,
                                                            horizontal: 12),
                                                    decoration: BoxDecoration(
                                                      color:
                                                          AppColors.background,
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              12),
                                                      border: Border.all(
                                                        color: const Color
                                                            .fromRGBO(
                                                            177, 145, 118, 0.3),
                                                      ),
                                                    ),
                                                    child: Row(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      children: [
                                                        Icon(
                                                            Icons
                                                                .person_outline,
                                                            color: AppColors
                                                                .primary),
                                                        const SizedBox(
                                                            width: 4),
                                                        Expanded(
                                                          child: Column(
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .center,
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .start,
                                                            children: [
                                                              Text(
                                                                homeprovider
                                                                        .guestsController
                                                                        .text
                                                                        .isNotEmpty
                                                                    ? homeprovider
                                                                        .guestsController
                                                                        .text
                                                                    : 'Number of guests',
                                                                style:
                                                                    TextStyle(
                                                                  color: Colors
                                                                          .grey[
                                                                      600],
                                                                  fontSize: 10,
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                          SizedBox(
                                            height: 20,
                                          ),
                                          Material(
                                            color: Colors.transparent,
                                            elevation: 4,
                                            borderRadius:
                                                BorderRadius.circular(12),
                                            child: GestureDetector(
                                              onTap: () async {
                                                final isValid = homeprovider
                                                    .isSearchValid();
                                                if (!isValid) {
                                                  ScaffoldMessenger.of(context)
                                                      .showSnackBar(
                                                    SnackBar(
                                                      content: const Text(
                                                          'Please select check-in and check-out dates'),
                                                      backgroundColor:
                                                          Colors.red,
                                                      behavior: SnackBarBehavior
                                                          .floating,
                                                      shape:
                                                          RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(10),
                                                      ),
                                                    ),
                                                  );
                                                  return;
                                                }

                                                // Show loader dialog
                                                showDialog(
                                                  context: context,
                                                  barrierDismissible: false,
                                                  builder: (_) => const Dialog(
                                                      backgroundColor:
                                                          Colors.transparent,
                                                      elevation: 0,
                                                      child:
                                                          StaggeredDotsAvatarLoader(
                                                              image: AssetImage(
                                                                  AppImages
                                                                      .logo))
                                                      // GlowingAvatarLoader(
                                                      //   imageUrl: AppImages.logo,
                                                      //   size: 100,
                                                      // ),
                                                      ),
                                                );

                                                // Wait 3 seconds
                                                await Future.delayed(
                                                    const Duration(seconds: 3));

                                                // Close the loader dialog
                                                if (context.mounted) {
                                                  Navigator.of(context).pop();
                                                }

                                                // Navigate to hotel list screen
                                                if (context.mounted) {
                                                  Navigator.push(
                                                    context,
                                                    MaterialPageRoute(
                                                      builder: (context) =>
                                                          const HotelListScreen(),
                                                    ),
                                                  );
                                                }
                                              },
                                              behavior: HitTestBehavior
                                                  .opaque, // Ensures the entire area is tappable
                                              child: Container(
                                                height: 50,
                                                width: double.infinity,
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 24,
                                                    vertical: 12),
                                                decoration: BoxDecoration(
                                                  color: AppColors.secondary,
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                ),
                                                child: Center(
                                                  child: Row(
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    children: [
                                                      Text(
                                                        'search.button'.tr,
                                                        style: TextStyle(
                                                          color: Colors.white,
                                                          fontSize: 16,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                        ),
                                                      ),
                                                      SizedBox(width: 8),
                                                      Icon(
                                                        Icons.search,
                                                        color: Colors.white,
                                                        size: 20,
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),

                                // Search button positioned as an overlay
                              ],
                            ),
                          ),
                          //  Positioned(
                          //         bottom:10,
                          //         right: 180,
                          //         child: Material(
                          //           color: Colors.transparent,
                          //           elevation: 4,
                          //           borderRadius: BorderRadius.circular(12),
                          //           child: GestureDetector(
                          //             onTap: () async {

                          //               final isValid = homeprovider.isSearchValid();
                          //               if (!isValid) {
                          //                 ScaffoldMessenger.of(context).showSnackBar(
                          //                   SnackBar(
                          //                     content: const Text('Please select check-in and check-out dates'),
                          //                     backgroundColor: Colors.red,
                          //                     behavior: SnackBarBehavior.floating,
                          //                     shape: RoundedRectangleBorder(
                          //                       borderRadius: BorderRadius.circular(10),
                          //                     ),
                          //                   ),
                          //                 );
                          //                 return;
                          //               }

                          //               // Show loader dialog
                          //               showDialog(
                          //                 context: context,
                          //                 barrierDismissible: false,
                          //                 builder: (_) => const Dialog(
                          //                   backgroundColor: Colors.transparent,
                          //                   elevation: 0,
                          //                   child: StaggeredDotsAvatarLoader(image: AssetImage(AppImages.logo))
                          //                   // GlowingAvatarLoader(
                          //                   //   imageUrl: AppImages.logo,
                          //                   //   size: 100,
                          //                   // ),
                          //                 ),
                          //               );

                          //               // Wait 3 seconds
                          //               await Future.delayed(const Duration(seconds: 3));

                          //               // Close the loader dialog
                          //               if (context.mounted) {
                          //                 Navigator.of(context).pop();
                          //               }

                          //               // Navigate to hotel list screen
                          //               if (context.mounted) {
                          //                 Navigator.push(
                          //                   context,
                          //                   MaterialPageRoute(
                          //                     builder: (context) => const HotelListScreen(),
                          //                   ),
                          //                 );
                          //               }
                          //             },
                          //             behavior: HitTestBehavior.opaque, // Ensures the entire area is tappable
                          //             child: Container(
                          //               height: 50,
                          //               padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                          //               decoration: BoxDecoration(
                          //                 color: AppColors.secondary,
                          //                 borderRadius: BorderRadius.circular(12),
                          //               ),
                          //               child: Row(
                          //                 mainAxisSize: MainAxisSize.min,
                          //                 children: [
                          //                   Text(
                          //                     'search.button'.tr,
                          //                     style: TextStyle(
                          //                       color: Colors.white,
                          //                       fontSize: 16,
                          //                       fontWeight: FontWeight.bold,
                          //                     ),
                          //                   ),
                          //                   SizedBox(width: 8),
                          //                   Icon(
                          //                     Icons.search,
                          //                     color: Colors.white,
                          //                     size: 20,
                          //                   ),
                          //                 ],
                          //               ),
                          //             ),
                          //           ),
                          //         ),
                          //       ),
                        ],
                      ),
                    ),

                    // Add spacing to account for the overlapping container
                    SizedBox(height: 20),

                    // Content section
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'search.popular_destinations'.tr,
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),

                          const SizedBox(height: 12),

                          // Horizontal scrollable list of destinations
                          SizedBox(
                            height: 180,
                            child: ListView(
                              scrollDirection: Axis.horizontal,
                              children: [
                                // Destination 1
                                _buildDestinationCard(
                                  'Maldives',
                                  'https://images.unsplash.com/photo-1573843981267-be1999ff37cd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8bWFsZGl2ZXN8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=500&q=60',
                                ),

                                // Destination 2
                                _buildDestinationCard(
                                  'Bali',
                                  'https://images.unsplash.com/photo-1537996194471-e657df975ab4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8YmFsaXxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=500&q=60',
                                ),

                                // Destination 3
                                _buildDestinationCard(
                                  'Santorini',
                                  'https://images.unsplash.com/photo-1570077188670-e3a8d69ac5ff?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8c2FudG9yaW5pfGVufDB8fDB8fHww&auto=format&fit=crop&w=500&q=60',
                                ),

                                // Destination 4
                                _buildDestinationCard(
                                  'Paris',
                                  'https://images.unsplash.com/photo-1502602898657-3e91760cbb34?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8cGFyaXN8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=500&q=60',
                                ),

                                // Destination 5
                                _buildDestinationCard(
                                  'Dubai',
                                  'https://images.unsplash.com/photo-1512453979798-5ea266f8880c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8ZHViYWl8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=500&q=60',
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(height: 24),

                          // Special Offers section - horizontally scrollable
                          Text(
                            'search.special_offers'.tr,
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),

                          const SizedBox(height: 12),

                          // Horizontal scrollable list of offers
                          SizedBox(
                            height: 55, // Further reduced height as requested
                            child: ListView(
                              scrollDirection: Axis.horizontal,
                              children: [
                                // Offer 1
                                SizedBox(
                                  width: 320, // Increased width
                                  child: _buildOfferStrip(
                                    'Summer Special',
                                    'Get 25% off on beach resorts',
                                    Colors.blue.shade700,
                                    Icons.beach_access,
                                  ),
                                ),

                                const SizedBox(width: 12),

                                // Offer 2
                                SizedBox(
                                  width: 320, // Increased width
                                  child: _buildOfferStrip(
                                    'Weekend Getaway',
                                    'Book now and save 15%',
                                    Colors.orange.shade800,
                                    Icons.weekend,
                                  ),
                                ),

                                const SizedBox(width: 12),

                                // Offer 3
                                SizedBox(
                                  width: 320, // Increased width
                                  child: _buildOfferStrip(
                                    'Family Package',
                                    'Kids stay free with 2 adults',
                                    Colors.green.shade700,
                                    Icons.family_restroom,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(height: 8),

                          // Luxury Advertisement container
                          Container(
                            width: double.infinity,
                            height:
                                80, // Keeping the reduced height to fix overflow
                            margin: EdgeInsets.symmetric(vertical: 0),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  Color(0xFF8A2387), // Rich purple
                                  Color(0xFFE94057), // Vibrant pink
                                  Color(0xFFF27121), // Warm orange
                                ],
                              ),
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: Color(0xFFE94057).withAlpha(60),
                                  blurRadius: 12,
                                  spreadRadius: 2,
                                  offset: Offset(0, 6),
                                ),
                              ],
                              border: Border.all(
                                color: Colors.white.withAlpha(30),
                                width: 0.5,
                              ),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(20),
                              child: Stack(
                                children: [
                                  // Luxury pattern overlay
                                  Opacity(
                                    opacity: 0.05,
                                    child: Container(
                                      width: double.infinity,
                                      height: double.infinity,
                                      decoration: BoxDecoration(
                                        image: DecorationImage(
                                          image: NetworkImage(
                                              'https://www.transparenttextures.com/patterns/diamond-upholstery.png'),
                                          repeat: ImageRepeat.repeat,
                                        ),
                                      ),
                                    ),
                                  ),

                                  // Decorative elements
                                  Positioned(
                                    top: -20,
                                    right: -20,
                                    child: Container(
                                      width: 80,
                                      height: 80,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        gradient: RadialGradient(
                                          colors: [
                                            Colors.white.withAlpha(50),
                                            Colors.white.withAlpha(0),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                  Positioned(
                                    bottom: -25,
                                    left: 30,
                                    child: Container(
                                      width: 60,
                                      height: 60,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        gradient: RadialGradient(
                                          colors: [
                                            Colors.white.withAlpha(30),
                                            Colors.white.withAlpha(0),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),

                                  // Gold accent line
                                  Positioned(
                                    top: 0,
                                    left: 20,
                                    right: 20,
                                    child: Container(
                                      height: 2,
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          begin: Alignment.centerLeft,
                                          end: Alignment.centerRight,
                                          colors: [
                                            Colors.transparent,
                                            Colors.amber.shade200,
                                            Colors.amber.shade400,
                                            Colors.amber.shade200,
                                            Colors.transparent,
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),

                                  // Ad content with luxury styling
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 16.0, vertical: 8.0),
                                    child: Row(
                                      children: [
                                        // Ad text with improved typography
                                        Expanded(
                                          flex: 3,
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Text(
                                                'Get 30% Off on Your First Booking',
                                                style: TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.bold,
                                                  letterSpacing: 0.5,
                                                  shadows: [
                                                    Shadow(
                                                      color: Colors.black
                                                          .withAlpha(100),
                                                      blurRadius: 2,
                                                      offset: Offset(1, 1),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              SizedBox(height: 4),
                                              Container(
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 12,
                                                    vertical: 4),
                                                decoration: BoxDecoration(
                                                  color: Colors.white,
                                                  borderRadius:
                                                      BorderRadius.circular(16),
                                                  boxShadow: [
                                                    BoxShadow(
                                                      color: Colors.black
                                                          .withAlpha(30),
                                                      blurRadius: 4,
                                                      offset: Offset(0, 2),
                                                    ),
                                                  ],
                                                ),
                                                child: Text(
                                                  'Use Code: FIRST30',
                                                  style: TextStyle(
                                                    color: Color(0xFF8A2387),
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: 10,
                                                    letterSpacing: 0.5,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),

                                        // Luxury gift icon
                                        Container(
                                          width: 50,
                                          height: 50,
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            color: Colors.white,
                                            boxShadow: [
                                              BoxShadow(
                                                color:
                                                    Colors.black.withAlpha(30),
                                                blurRadius: 6,
                                                offset: Offset(0, 3),
                                              ),
                                            ],
                                            border: Border.all(
                                              color: Colors.amber.shade200,
                                              width: 1,
                                            ),
                                          ),
                                          child: Center(
                                            child: Icon(
                                              Icons.card_giftcard,
                                              size: 28,
                                              color: Color(0xFFE94057),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),

                          const SizedBox(height: 8),

                          // Tour Packages section
                          Text(
                            'search.tour_packages'.tr,
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),

                          const SizedBox(height: 16),

                          // Tour package 1
                          _buildTourPackage(
                            'Mountain Retreat',
                            'Swiss Alps',
                            '\$199',
                            '\$265',
                            '25% OFF',
                            'https://images.unsplash.com/photo-1539367628448-4bc5c9d171c8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8YmFsaXxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=500&q=60',
                          ),

                          const SizedBox(height: 16),

                          // Tour package 2
                          _buildTourPackage(
                            'Beach Paradise',
                            'Maldives',
                            '\$299',
                            '\$375',
                            '20% OFF',
                            'https://images.unsplash.com/photo-1499856871958-5b9627545d1a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8cGFyaXN8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=500&q=60',
                          ),

                          const SizedBox(height: 16),

                          // Tour package 3
                          _buildTourPackage(
                            'Desert Oasis',
                            'Dubai',
                            '\$249',
                            '\$305',
                            '18% OFF',
                            'https://images.unsplash.com/photo-1518684079-3c830dcef090?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8ZHViYWl8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=500&q=60',
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ));
        });
      },
    );
  }
}
